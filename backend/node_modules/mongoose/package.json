{"name": "mongoose", "description": "Mongoose MongoDB ODM", "version": "5.13.23", "author": "<PERSON> <<EMAIL>>", "keywords": ["mongodb", "document", "model", "schema", "database", "odm", "data", "datastore", "query", "nosql", "orm", "db"], "license": "MIT", "dependencies": {"@types/bson": "1.x || 4.0.x", "@types/mongodb": "^3.5.27", "bson": "^1.1.4", "kareem": "2.3.2", "mongodb": "3.7.4", "mongoose-legacy-pluralize": "1.0.2", "mpath": "0.8.4", "mquery": "3.2.5", "ms": "2.1.2", "optional-require": "1.0.x", "regexp-clone": "1.0.0", "safe-buffer": "5.2.1", "sift": "13.5.2", "sliced": "1.0.1"}, "devDependencies": {"@babel/core": "7.10.5", "@babel/preset-env": "7.10.4", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "@types/babel-types": "7.0.11", "@types/babylon": "6.16.6", "@types/json-schema": "7.0.3", "@types/node": "16.11.23", "acquit": "1.x", "acquit-ignore": "0.1.x", "acquit-require": "0.1.x", "babel-loader": "8.1.0", "benchmark": "2.1.4", "bluebird": "3.5.5", "chalk": "2.4.2", "cheerio": "1.0.0-rc.2", "co": "4.6.0", "dox": "0.3.1", "eslint": "7.1.0", "eslint-plugin-mocha-no-only": "1.1.0", "highlight.js": "9.18.2", "lodash.isequal": "4.5.0", "lodash.isequalwith": "4.4.0", "marked": "1.1.1", "mkdirp": "0.5.5", "mocha": "5.x", "moment": "2.x", "node-static": "0.7.11", "object-sizeof": "1.3.0", "pug": "2.0.3", "q": "1.5.1", "rimraf": "2.6.3", "schema-utils": "2.6.5", "semver": "5.5.0", "typescript": "4.1.x", "uuid": "2.0.3", "uuid-parse": "1.0.0", "validator": "10.8.0", "webpack": "4.44.0"}, "directories": {"lib": "./lib/mongoose"}, "scripts": {"lint": "eslint .", "build-browser": "node build-browser.js", "prepublishOnly": "npm run build-browser", "release": "git pull && git push origin master --tags && npm publish", "release-legacy": "git pull origin 5.x && git push origin 5.x --tags && npm publish --tag legacy", "test": "mocha --exit ./test/*.test.js ./test/typescript/main.test.js", "tdd": "mocha ./test/*.test.js ./test/typescript/main.test.js --watch --recursive --watch-files ./**/*.js", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "main": "./index.js", "types": "./index.d.ts", "engines": {"node": ">=4.0.0"}, "bugs": {"url": "https://github.com/Automattic/mongoose/issues/new"}, "repository": {"type": "git", "url": "git://github.com/Automattic/mongoose.git"}, "homepage": "https://mongoosejs.com", "browser": "./dist/browser.umd.js", "mocha": {"extension": ["test.js"], "watch-files": ["test/**/*.js"]}, "eslintConfig": {"extends": ["eslint:recommended"], "overrides": [{"files": ["**/*.{ts,tsx}"], "extends": ["plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-types": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/explicit-module-boundary-types": "off"}}], "plugins": ["mocha-no-only"], "parserOptions": {"ecmaVersion": 2015}, "env": {"node": true, "es6": true}, "rules": {"comma-style": "error", "indent": ["error", 2, {"SwitchCase": 1, "VariableDeclarator": 2}], "keyword-spacing": "error", "no-whitespace-before-property": "error", "no-buffer-constructor": "warn", "no-console": "off", "no-multi-spaces": "error", "no-constant-condition": "off", "func-call-spacing": "error", "no-trailing-spaces": "error", "no-undef": "error", "no-unneeded-ternary": "error", "no-const-assign": "error", "no-useless-rename": "error", "no-dupe-keys": "error", "space-in-parens": ["error", "never"], "spaced-comment": ["error", "always", {"block": {"markers": ["!"], "balanced": true}}], "key-spacing": ["error", {"beforeColon": false, "afterColon": true}], "comma-spacing": ["error", {"before": false, "after": true}], "array-bracket-spacing": 1, "arrow-spacing": ["error", {"before": true, "after": true}], "object-curly-spacing": ["error", "always"], "comma-dangle": ["error", "never"], "no-unreachable": "error", "quotes": ["error", "single"], "quote-props": ["error", "as-needed"], "semi": "error", "no-extra-semi": "error", "semi-spacing": "error", "no-spaced-func": "error", "no-throw-literal": "error", "space-before-blocks": "error", "space-before-function-paren": ["error", "never"], "space-infix-ops": "error", "space-unary-ops": "error", "no-var": "warn", "prefer-const": "warn", "strict": ["error", "global"], "no-restricted-globals": ["error", {"name": "context", "message": "Don't use <PERSON><PERSON>'s global context"}], "no-prototype-builtins": "off", "mocha-no-only/mocha-no-only": ["error"]}}, "funding": {"type": "opencollective", "url": "https://opencollective.com/mongoose"}}