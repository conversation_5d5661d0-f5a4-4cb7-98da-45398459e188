{"name": "mongodb", "version": "3.7.4", "description": "The official MongoDB driver for Node.js", "main": "index.js", "files": ["index.js", "lib"], "repository": {"type": "git", "url": "**************:mongodb/node-mongodb-native.git"}, "keywords": ["mongodb", "driver", "official"], "author": {"name": "The MongoDB NodeJS Team", "email": "<EMAIL>"}, "peerDependenciesMeta": {"kerberos": {"optional": true}, "mongodb-client-encryption": {"optional": true}, "mongodb-extjson": {"optional": true}, "snappy": {"optional": true}, "bson-ext": {"optional": true}, "aws4": {"optional": true}}, "dependencies": {"bl": "^2.2.1", "bson": "^1.1.4", "denque": "^1.4.1", "optional-require": "^1.1.8", "safe-buffer": "^5.1.2"}, "devDependencies": {"@types/chai": "^4.2.16", "@types/mocha": "^8.2.2", "@types/node": "^14.14.37", "array-includes": "^3.1.3", "chai": "^4.1.1", "chai-subset": "^1.6.0", "chalk": "^2.4.2", "co": "4.6.0", "eslint": "^7.10.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-es": "^3.0.1", "eslint-plugin-prettier": "^3.1.3", "jsdoc": "^3.5.5", "lodash.camelcase": "^4.3.0", "mocha": "5.2.0", "mocha-sinon": "^2.1.0", "mongodb-extjson": "^2.1.1", "mongodb-mock-server": "^1.0.1", "nyc": "^15.1.0", "object.entries": "^1.1.3", "prettier": "^1.19.1", "semver": "^5.5.0", "sinon": "^4.3.0", "sinon-chai": "^3.2.0", "snappy": "^6.3.4", "spec-xunit-file": "0.0.1-3", "standard-version": "^9.2.0", "tslib": "^2.2.0", "typescript": "^4.2.4", "util.promisify": "^1.0.1", "worker-farm": "^1.5.0", "wtfnode": "^0.8.0", "yargs": "^14.2.0"}, "license": "Apache-2.0", "engines": {"node": ">=4"}, "bugs": {"url": "https://jira.mongodb.org/projects/NODE/issues/"}, "scripts": {"build:evergreen": "node .evergreen/generate_evergreen_tasks.js", "build:unified": "tsc -p test/functional/unified-spec-runner/tsconfig.unified.json", "check:atlas": "mocha --opts '{}' ./test/manual/atlas_connectivity.test.js", "check:bench": "node test/benchmarks/driverBench/", "check:coverage": "nyc npm run check:test", "check:kerberos": "mocha --opts '{}' -t 60000 test/manual/kerberos.test.js", "check:ldap": "mocha --opts '{}' test/manual/ldap.test.js", "check:lint": "eslint -v && eslint lib test", "check:test": "mocha --recursive test/functional test/unit", "check:tls": "mocha --opts '{}' test/manual/tls_support.test.js", "format": "npm run check:lint -- --fix", "release": "standard-version -i HISTORY.md", "test": "npm run check:lint && mocha --recursive test/functional test/unit"}, "homepage": "https://github.com/mongodb/node-mongodb-native", "optionalDependencies": {"saslprep": "^1.0.0"}}