!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n=n||self).sift={})}(this,(function(n){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var t=function(n,r){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,t){n.__proto__=t}||function(n,t){for(var r in t)t.hasOwnProperty(r)&&(n[r]=t[r])})(n,r)};function r(n,r){function i(){this.constructor=n}t(n,r),n.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}var i=function(n){var t="[object "+n+"]";return function(n){return u(n)===t}},u=function(n){return Object.prototype.toString.call(n)},e=function(n){return n instanceof Date?n.getTime():o(n)?n.map(e):n&&"function"==typeof n.toJSON?n.toJSON():n},o=i("Array"),f=i("Object"),c=i("Function"),s=function(n,t){if(null==n&&n==t)return!0;if(n===t)return!0;if(Object.prototype.toString.call(n)!==Object.prototype.toString.call(t))return!1;if(o(n)){if(n.length!==t.length)return!1;for(var r=0,i=n.length;r<i;r++)if(!s(n[r],t[r]))return!1;return!0}if(f(n)){if(Object.keys(n).length!==Object.keys(t).length)return!1;for(var u in n)if(!s(n[u],t[u]))return!1;return!0}return!1},h=function(n,t,r,i,u,e){var f=t[i];if(o(n)&&isNaN(Number(f)))for(var c=0,s=n.length;c<s;c++)if(!h(n[c],t,r,i,c,n))return!1;return i===t.length||null==n?r(n,u,e):h(n[f],t,r,i+1,f,n)},a=function(){function n(n,t,r){this.params=n,this.owneryQuery=t,this.options=r,this.init()}return n.prototype.init=function(){},n.prototype.reset=function(){this.done=!1,this.keep=!1},n}(),l=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i)||this;return e.name=u,e}return r(t,n),t}(a),v=function(n){function t(t,r,i,u){var e=n.call(this,t,r,i)||this;return e.children=u,e}return r(t,n),t.prototype.reset=function(){this.keep=!1,this.done=!1;for(var n=0,t=this.children.length;n<t;n++)this.children[n].reset()},t.prototype.childrenNext=function(n,t,r){for(var i=!0,u=!0,e=0,o=this.children.length;e<o;e++){var f=this.children[e];if(f.next(n,t,r),f.keep||(u=!1),f.done){if(!f.keep)break}else i=!1}this.done=i,this.keep=u},t}(a),w=function(n){function t(t,r,i,u,e){var o=n.call(this,t,r,i,u)||this;return o.name=e,o}return r(t,n),t}(v),p=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.next=function(n,t,r){this.childrenNext(n,t,r)},t}(v),$=function(n){function t(t,r,i,u,e){var o=n.call(this,r,i,u,e)||this;return o.keyPath=t,o.t=function(n,t,r){return o.childrenNext(n,t,r),!o.done},o}return r(t,n),t.prototype.next=function(n,t,r){h(n,this.keyPath,this.t,0,t,r)},t}(v),b=function(n,t){if(n instanceof Function)return n;if(n instanceof RegExp)return function(t){var r="string"==typeof t&&n.test(t);return n.lastIndex=0,r};var r=e(n);return function(n){return t(r,e(n))}},d=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.init=function(){this.i=b(this.params,this.options.compare)},t.prototype.next=function(n,t,r){Array.isArray(r)&&!r.hasOwnProperty(t)||this.i(n,t,r)&&(this.done=!0,this.keep=!0)},t}(a),y=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.next=function(){this.done=!0,this.keep=!1},t}(a),j=function(n){return t=function(t,r,i){var u=typeof e(t),o=n(t);return new d((function(n){return typeof e(n)===u&&o(n)}),r,i)},function(n,r,i,u){return null==n?new y(n,r,i):t(n,r,i,u)};var t},O=function(n,t,r,i){var u=i.operations[n];if(!u)throw new Error("Unsupported operation: "+n);return u(t,r,i,n)},m=function(n){for(var t in n)if("$"===t.charAt(0))return!0;return!1},g=function(n,t,r,i){if(m(t)){var u=x(t,i),e=u[0];if(u[1].length)throw new Error("Property queries must contain only operations, or exact objects.");return new $(n,t,r,i,e)}return new $(n,t,r,i,[new d(t,r,i)])},_=function(n,t,r){void 0===t&&(t=null);var i=void 0===r?{}:r,u=i.compare,e=i.operations,o={compare:u||s,operations:Object.assign({},e||{})},f=x(n,o),c=f[0],h=f[1],a=[];return c.length&&a.push(new $([],n,t,o,c)),a.push.apply(a,h),1===a.length?a[0]:new p(n,t,o,a)},x=function(n,t){var r,i=[],u=[];if(!(r=n)||r.constructor!==Object&&r.constructor!==Array&&"function Object() { [native code] }"!==r.constructor.toString()&&"function Array() { [native code] }"!==r.constructor.toString()||r.toJSON)return i.push(new d(n,n,t)),[i,u];for(var e in n)if("$"===e.charAt(0)){var o=O(e,n[e],n,t);null!=o&&i.push(o)}else u.push(g(e.split("."),n[e],n,t));return[i,u]},E=function(n){return function(t,r,i){return n.reset(),n.next(t,r,i),n.keep}},A=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.init=function(){this.i=b(this.params,this.options.compare)},t.prototype.reset=function(){n.prototype.reset.call(this),this.keep=!0},t.prototype.next=function(n){this.i(n)&&(this.done=!0,this.keep=!1)},t}(l),k=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.init=function(){this.u=_(this.params,this.owneryQuery,this.options)},t.prototype.reset=function(){n.prototype.reset.call(this),this.u.reset()},t.prototype.next=function(n){if(o(n)){for(var t=0,r=n.length;t<r;t++)this.u.reset(),this.u.next(n[t],t,n),this.keep=this.keep||this.u.keep;this.done=!0}else this.done=!1,this.keep=!1},t}(l),z=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.init=function(){this.u=_(this.params,this.owneryQuery,this.options)},t.prototype.reset=function(){this.u.reset()},t.prototype.next=function(n,t,r){this.u.next(n,t,r),this.done=this.u.done,this.keep=!this.u.keep},t}(l),F=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.init=function(){},t.prototype.next=function(n){o(n)&&n.length===this.params&&(this.done=!0,this.keep=!0)},t}(l),N=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.init=function(){var n=this;this.o=this.params.map((function(t){return _(t,null,n.options)}))},t.prototype.reset=function(){this.done=!1,this.keep=!1;for(var n=0,t=this.o.length;n<t;n++)this.o[n].reset()},t.prototype.next=function(n,t,r){for(var i=!1,u=!1,e=0,o=this.o.length;e<o;e++){var f=this.o[e];if(f.next(n,t,r),f.keep){i=!0,u=f.keep;break}}this.keep=u,this.done=i},t}(l),q=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.next=function(t,r,i){n.prototype.next.call(this,t,r,i),this.keep=!this.keep},t}(N),D=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.init=function(){var n=this;this.h=this.params.map((function(t){if(m(t))throw new Error("cannot nest $ under "+n.constructor.name.toLowerCase());return b(t,n.options.compare)}))},t.prototype.next=function(n,t,r){for(var i=!1,u=!1,e=0,o=this.h.length;e<o;e++){if((0,this.h[e])(n)){i=!0,u=!0;break}}this.keep=u,this.done=i},t}(l),M=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.next=function(t,r,i){n.prototype.next.call(this,t,r,i),this.keep=!this.keep},t}(D),P=function(n){function t(){return null!==n&&n.apply(this,arguments)||this}return r(t,n),t.prototype.next=function(n,t,r){r.hasOwnProperty(t)===this.params&&(this.done=!0,this.keep=!0)},t}(l),R=function(n){function t(t,r,i,u){return n.call(this,t,r,i,t.map((function(n){return _(n,r,i)})),u)||this}return r(t,n),t.prototype.next=function(n,t,r){this.childrenNext(n,t,r)},t}(w),S=function(n,t,r){return new d(n,t,r)},C=function(n,t,r,i){return new A(n,t,r,i)},I=function(n,t,r,i){return new N(n,t,r,i)},T=function(n,t,r,i){return new q(n,t,r,i)},U=function(n,t,r,i){return new k(n,t,r,i)},B=function(n,t,r,i){return new M(n,t,r,i)},G=function(n,t,r,i){return new D(n,t,r,i)},H=j((function(n){return function(t){return t<n}})),J=j((function(n){return function(t){return t<=n}})),K=j((function(n){return function(t){return t>n}})),L=j((function(n){return function(t){return t>=n}})),Q=function(n,t,r){var i=n[0],u=n[1];return new d((function(n){return e(n)%i===u}),t,r)},V=function(n,t,r,i){return new P(n,t,r,i)},W=function(n,t,r){return new d(new RegExp(n,t.$options),t,r)},X=function(n,t,r,i){return new z(n,t,r,i)},Y={number:function(n){return"number"==typeof n},string:function(n){return"string"==typeof n},bool:function(n){return"boolean"==typeof n},array:function(n){return Array.isArray(n)},null:function(n){return null===n},timestamp:function(n){return n instanceof Date}},Z=function(n,t,r){return new d((function(t){if("string"==typeof n){if(!Y[n])throw new Error("Type alias does not exist");return Y[n](t)}return null!=t&&(t instanceof n||t.constructor===n)}),t,r)},nn=function(n,t,r,i){return new R(n,t,r,i)},tn=nn,rn=function(n,t,r){return new F(n,t,r,"$size")},un=function(){return null},en=function(n,t,r){var i;if(c(n))i=n;else{if(process.env.CSP_ENABLED)throw new Error('In CSP mode, sift does not support strings in "$where" condition');i=new Function("obj","return "+n)}return new d((function(n){return i.bind(n)(n)}),t,r)},on=Object.freeze({__proto__:null,$Size:F,$eq:S,$ne:C,$or:I,$nor:T,$elemMatch:U,$nin:B,$in:G,$lt:H,$lte:J,$gt:K,$gte:L,$mod:Q,$exists:V,$regex:W,$not:X,$type:Z,$and:nn,$all:tn,$size:rn,$options:un,$where:en}),fn=function(n,t,r){var i=void 0===r?{}:r,u=i.compare,e=i.operations;return _(n,t,{compare:u,operations:Object.assign({},on,e||{})})};n.$Size=F,n.$all=tn,n.$and=nn,n.$elemMatch=U,n.$eq=S,n.$exists=V,n.$gt=K,n.$gte=L,n.$in=G,n.$lt=H,n.$lte=J,n.$mod=Q,n.$ne=C,n.$nin=B,n.$nor=T,n.$not=X,n.$options=un,n.$or=I,n.$regex=W,n.$size=rn,n.$type=Z,n.$where=en,n.EqualsOperation=d,n.createDefaultQueryOperation=fn,n.createEqualsOperation=function(n,t,r){return new d(n,t,r)},n.createOperationTester=E,n.createQueryOperation=_,n.createQueryTester=function(n,t){return void 0===t&&(t={}),E(_(n,null,t))},n.default=function(n,t){void 0===t&&(t={});var r=fn(n,null,t);return E(r)},Object.defineProperty(n,"l",{value:!0})}));
//# sourceMappingURL=sift.min.js.map
