{"name": "sift", "description": "MongoDB query filtering in JavaScript", "version": "13.5.2", "repository": "crcn/sift.js", "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "engines": {}, "typings": "./index.d.ts", "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "devDependencies": {"@rollup/plugin-replace": "^2.3.2", "@rollup/plugin-typescript": "^4.1.1", "@types/node": "^13.7.0", "bson": "^4.0.3", "eval": "^0.1.4", "husky": "^1.2.1", "immutable": "^3.7.6", "mocha": "8.3.2", "mongodb": "^3.6.6", "prettier": "1.15.3", "pretty-quick": "^1.11.1", "rimraf": "^3.0.2", "rollup": "^2.7.2", "rollup-plugin-terser": "^7.0.2", "tslib": "^2.0.0", "typescript": "^3.8.3"}, "main": "./index.js", "module": "./es5m/index.js", "es2015": "./es/index.js", "scripts": {"clean": "rimraf lib es5m es", "prebuild": "npm run clean && npm run build:types", "build": "rollup -c", "build:types": "tsc -p tsconfig.json --emitDeclarationOnly --outDir lib", "test": "npm run test:spec && npm run test:types", "test:spec": "mocha ./test -R spec", "test:types": "cd test && tsc types.ts --noEmit", "prepublishOnly": "npm run build && npm run test"}, "files": ["es", "es5m", "lib", "*.d.ts", "*.js.map", "index.js", "sift.csp.min.js", "sift.min.js", "MIT-LICENSE.txt"]}