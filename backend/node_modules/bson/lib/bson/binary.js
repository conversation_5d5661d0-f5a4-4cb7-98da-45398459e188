/**
 * Module dependencies.
 * @ignore
 */

// Test if we're in Node via presence of "global" not absence of "window"
// to support hybrid environments like Electron
if (typeof global !== 'undefined') {
  var Buffer = require('buffer').Buffer; // TODO just use global Buffer
}

var utils = require('./parser/utils');

/**
 * A class representation of the BSON Binary type.
 *
 * Sub types
 *  - **BSON.BSON_BINARY_SUBTYPE_DEFAULT**, default BSON type.
 *  - **BSON.BSON_BINARY_SUBTYPE_FUNCTION**, BSON function type.
 *  - **BSON.BSON_BINARY_SUBTYPE_BYTE_ARRAY**, BSON byte array type.
 *  - **BSON.BSON_BINARY_SUBTYPE_UUID**, BSON uuid type.
 *  - **BSON.BSON_BINARY_SUBTYPE_MD5**, BSON md5 type.
 *  - **BSON.BSON_BINARY_SUBTYPE_USER_DEFINED**, BSON user defined type.
 *
 * @class
 * @param {<PERSON>uffer} buffer a buffer object containing the binary data.
 * @param {Number} [subType] the option binary type.
 * @return {Binary}
 */
function Binary(buffer, subType) {
  if (!(this instanceof Binary)) return new Binary(buffer, subType);

  if (
    buffer != null &&
    !(typeof buffer === 'string') &&
    !Buffer.isBuffer(buffer) &&
    !(buffer instanceof Uint8Array) &&
    !Array.isArray(buffer)
  ) {
    throw new Error('only String, Buffer, Uint8Array or Array accepted');
  }

  this._bsontype = 'Binary';

  if (buffer instanceof Number) {
    this.sub_type = buffer;
    this.position = 0;
  } else {
    this.sub_type = subType == null ? BSON_BINARY_SUBTYPE_DEFAULT : subType;
    this.position = 0;
  }

  if (buffer != null && !(buffer instanceof Number)) {
    // Only accept Buffer, Uint8Array or Arrays
    if (typeof buffer === 'string') {
      // Different ways of writing the length of the string for the different types
      if (typeof Buffer !== 'undefined') {
        this.buffer = utils.toBuffer(buffer);
      } else if (
        typeof Uint8Array !== 'undefined' ||
        Object.prototype.toString.call(buffer) === '[object Array]'
      ) {
        this.buffer = writeStringToArray(buffer);
      } else {
        throw new Error('only String, Buffer, Uint8Array or Array accepted');
      }
    } else {
      this.buffer = buffer;
    }
    this.position = buffer.length;
  } else {
    if (typeof Buffer !== 'undefined') {
      this.buffer = utils.allocBuffer(Binary.BUFFER_SIZE);
    } else if (typeof Uint8Array !== 'undefined') {
      this.buffer = new Uint8Array(new ArrayBuffer(Binary.BUFFER_SIZE));
    } else {
      this.buffer = new Array(Binary.BUFFER_SIZE);
    }
    // Set position to start of buffer
    this.position = 0;
  }
}

/**
 * Updates this binary with byte_value.
 *
 * @method
 * @param {string} byte_value a single byte we wish to write.
 */
Binary.prototype.put = function put(byte_value) {
  // If it's a string and a has more than one character throw an error
  if (byte_value['length'] != null && typeof byte_value !== 'number' && byte_value.length !== 1)
    throw new Error('only accepts single character String, Uint8Array or Array');
  if ((typeof byte_value !== 'number' && byte_value < 0) || byte_value > 255)
    throw new Error('only accepts number in a valid unsigned byte range 0-255');

  // Decode the byte value once
  var decoded_byte = null;
  if (typeof byte_value === 'string') {
    decoded_byte = byte_value.charCodeAt(0);
  } else if (byte_value['length'] != null) {
    decoded_byte = byte_value[0];
  } else {
    decoded_byte = byte_value;
  }

  if (this.buffer.length > this.position) {
    this.buffer[this.position++] = decoded_byte;
  } else {
    if (typeof Buffer !== 'undefined' && Buffer.isBuffer(this.buffer)) {
      // Create additional overflow buffer
      var buffer = utils.allocBuffer(Binary.BUFFER_SIZE + this.buffer.length);
      // Combine the two buffers together
      this.buffer.copy(buffer, 0, 0, this.buffer.length);
      this.buffer = buffer;
      this.buffer[this.position++] = decoded_byte;
    } else {
      buffer = null;
      // Create a new buffer (typed or normal array)
      if (Object.prototype.toString.call(this.buffer) === '[object Uint8Array]') {
        buffer = new Uint8Array(new ArrayBuffer(Binary.BUFFER_SIZE + this.buffer.length));
      } else {
        buffer = new Array(Binary.BUFFER_SIZE + this.buffer.length);
      }

      // We need to copy all the content to the new array
      for (var i = 0; i < this.buffer.length; i++) {
        buffer[i] = this.buffer[i];
      }

      // Reassign the buffer
      this.buffer = buffer;
      // Write the byte
      this.buffer[this.position++] = decoded_byte;
    }
  }
};

/**
 * Writes a buffer or string to the binary.
 *
 * @method
 * @param {(Buffer|string)} string a string or buffer to be written to the Binary BSON object.
 * @param {number} offset specify the binary of where to write the content.
 * @return {null}
 */
Binary.prototype.write = function write(string, offset) {
  offset = typeof offset === 'number' ? offset : this.position;

  // If the buffer is to small let's extend the buffer
  if (this.buffer.length < offset + string.length) {
    var buffer = null;
    // If we are in node.js
    if (typeof Buffer !== 'undefined' && Buffer.isBuffer(this.buffer)) {
      buffer = utils.allocBuffer(this.buffer.length + string.length);
      this.buffer.copy(buffer, 0, 0, this.buffer.length);
    } else if (Object.prototype.toString.call(this.buffer) === '[object Uint8Array]') {
      // Create a new buffer
      buffer = new Uint8Array(new ArrayBuffer(this.buffer.length + string.length));
      // Copy the content
      for (var i = 0; i < this.position; i++) {
        buffer[i] = this.buffer[i];
      }
    }

    // Assign the new buffer
    this.buffer = buffer;
  }

  if (typeof Buffer !== 'undefined' && Buffer.isBuffer(string) && Buffer.isBuffer(this.buffer)) {
    string.copy(this.buffer, offset, 0, string.length);
    this.position = offset + string.length > this.position ? offset + string.length : this.position;
    // offset = string.length
  } else if (
    typeof Buffer !== 'undefined' &&
    typeof string === 'string' &&
    Buffer.isBuffer(this.buffer)
  ) {
    this.buffer.write(string, offset, 'binary');
    this.position = offset + string.length > this.position ? offset + string.length : this.position;
    // offset = string.length;
  } else if (
    Object.prototype.toString.call(string) === '[object Uint8Array]' ||
    (Object.prototype.toString.call(string) === '[object Array]' && typeof string !== 'string')
  ) {
    for (i = 0; i < string.length; i++) {
      this.buffer[offset++] = string[i];
    }

    this.position = offset > this.position ? offset : this.position;
  } else if (typeof string === 'string') {
    for (i = 0; i < string.length; i++) {
      this.buffer[offset++] = string.charCodeAt(i);
    }

    this.position = offset > this.position ? offset : this.position;
  }
};

/**
 * Reads **length** bytes starting at **position**.
 *
 * @method
 * @param {number} position read from the given position in the Binary.
 * @param {number} length the number of bytes to read.
 * @return {Buffer}
 */
Binary.prototype.read = function read(position, length) {
  length = length && length > 0 ? length : this.position;

  // Let's return the data based on the type we have
  if (this.buffer['slice']) {
    return this.buffer.slice(position, position + length);
  } else {
    // Create a buffer to keep the result
    var buffer =
      typeof Uint8Array !== 'undefined'
        ? new Uint8Array(new ArrayBuffer(length))
        : new Array(length);
    for (var i = 0; i < length; i++) {
      buffer[i] = this.buffer[position++];
    }
  }
  // Return the buffer
  return buffer;
};

/**
 * Returns the value of this binary as a string.
 *
 * @method
 * @return {string}
 */
Binary.prototype.value = function value(asRaw) {
  asRaw = asRaw == null ? false : asRaw;

  // Optimize to serialize for the situation where the data == size of buffer
  if (
    asRaw &&
    typeof Buffer !== 'undefined' &&
    Buffer.isBuffer(this.buffer) &&
    this.buffer.length === this.position
  )
    return this.buffer;

  // If it's a node.js buffer object
  if (typeof Buffer !== 'undefined' && Buffer.isBuffer(this.buffer)) {
    return asRaw
      ? this.buffer.slice(0, this.position)
      : this.buffer.toString('binary', 0, this.position);
  } else {
    if (asRaw) {
      // we support the slice command use it
      if (this.buffer['slice'] != null) {
        return this.buffer.slice(0, this.position);
      } else {
        // Create a new buffer to copy content to
        var newBuffer =
          Object.prototype.toString.call(this.buffer) === '[object Uint8Array]'
            ? new Uint8Array(new ArrayBuffer(this.position))
            : new Array(this.position);
        // Copy content
        for (var i = 0; i < this.position; i++) {
          newBuffer[i] = this.buffer[i];
        }
        // Return the buffer
        return newBuffer;
      }
    } else {
      return convertArraytoUtf8BinaryString(this.buffer, 0, this.position);
    }
  }
};

/**
 * Length.
 *
 * @method
 * @return {number} the length of the binary.
 */
Binary.prototype.length = function length() {
  return this.position;
};

/**
 * @ignore
 */
Binary.prototype.toJSON = function() {
  return this.buffer != null ? this.buffer.toString('base64') : '';
};

/**
 * @ignore
 */
Binary.prototype.toString = function(format) {
  return this.buffer != null ? this.buffer.slice(0, this.position).toString(format) : '';
};

/**
 * Binary default subtype
 * @ignore
 */
var BSON_BINARY_SUBTYPE_DEFAULT = 0;

/**
 * @ignore
 */
var writeStringToArray = function(data) {
  // Create a buffer
  var buffer =
    typeof Uint8Array !== 'undefined'
      ? new Uint8Array(new ArrayBuffer(data.length))
      : new Array(data.length);
  // Write the content to the buffer
  for (var i = 0; i < data.length; i++) {
    buffer[i] = data.charCodeAt(i);
  }
  // Write the string to the buffer
  return buffer;
};

/**
 * Convert Array ot Uint8Array to Binary String
 *
 * @ignore
 */
var convertArraytoUtf8BinaryString = function(byteArray, startIndex, endIndex) {
  var result = '';
  for (var i = startIndex; i < endIndex; i++) {
    result = result + String.fromCharCode(byteArray[i]);
  }
  return result;
};

Binary.BUFFER_SIZE = 256;

/**
 * Default BSON type
 *
 * @classconstant SUBTYPE_DEFAULT
 **/
Binary.SUBTYPE_DEFAULT = 0;
/**
 * Function BSON type
 *
 * @classconstant SUBTYPE_DEFAULT
 **/
Binary.SUBTYPE_FUNCTION = 1;
/**
 * Byte Array BSON type
 *
 * @classconstant SUBTYPE_DEFAULT
 **/
Binary.SUBTYPE_BYTE_ARRAY = 2;
/**
 * OLD UUID BSON type
 *
 * @classconstant SUBTYPE_DEFAULT
 **/
Binary.SUBTYPE_UUID_OLD = 3;
/**
 * UUID BSON type
 *
 * @classconstant SUBTYPE_DEFAULT
 **/
Binary.SUBTYPE_UUID = 4;
/**
 * MD5 BSON type
 *
 * @classconstant SUBTYPE_DEFAULT
 **/
Binary.SUBTYPE_MD5 = 5;
/**
 * User BSON type
 *
 * @classconstant SUBTYPE_DEFAULT
 **/
Binary.SUBTYPE_USER_DEFINED = 128;

/**
 * Expose.
 */
module.exports = Binary;
module.exports.Binary = Binary;
