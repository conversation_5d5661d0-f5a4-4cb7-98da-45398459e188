const { pool } = require('../config/database');

// Services CRUD operations
const servicesService = {
  // Get all services
  getAll: async () => {
    const query = 'SELECT * FROM services ORDER BY created_at DESC';
    const result = await pool.query(query);
    return result.rows;
  },

  // Get service by ID
  getById: async (id) => {
    const query = 'SELECT * FROM services WHERE id = $1';
    const result = await pool.query(query, [id]);
    return result.rows[0];
  },

  // Create new service
  create: async (serviceData) => {
    const { title, description, icon } = serviceData;
    const query = `
      INSERT INTO services (title, description, icon) 
      VALUES ($1, $2, $3) 
      RETURNING *
    `;
    const result = await pool.query(query, [title, description, icon]);
    return result.rows[0];
  },

  // Update service
  update: async (id, serviceData) => {
    const { title, description, icon } = serviceData;
    const query = `
      UPDATE services 
      SET title = $1, description = $2, icon = $3, updated_at = CURRENT_TIMESTAMP 
      WHERE id = $4 
      RETURNING *
    `;
    const result = await pool.query(query, [title, description, icon, id]);
    return result.rows[0];
  },

  // Delete service
  delete: async (id) => {
    const query = 'DELETE FROM services WHERE id = $1 RETURNING *';
    const result = await pool.query(query, [id]);
    return result.rows[0];
  }
};

// Projects CRUD operations
const projectsService = {
  // Get all projects
  getAll: async () => {
    const query = 'SELECT * FROM projects ORDER BY created_at DESC';
    const result = await pool.query(query);
    return result.rows;
  },

  // Get project by ID
  getById: async (id) => {
    const query = 'SELECT * FROM projects WHERE id = $1';
    const result = await pool.query(query, [id]);
    return result.rows[0];
  },

  // Create new project
  create: async (projectData) => {
    const { title, description, tech_stack, image_url, project_url, github_url } = projectData;
    const query = `
      INSERT INTO projects (title, description, tech_stack, image_url, project_url, github_url) 
      VALUES ($1, $2, $3, $4, $5, $6) 
      RETURNING *
    `;
    const result = await pool.query(query, [title, description, tech_stack, image_url, project_url, github_url]);
    return result.rows[0];
  },

  // Update project
  update: async (id, projectData) => {
    const { title, description, tech_stack, image_url, project_url, github_url } = projectData;
    const query = `
      UPDATE projects 
      SET title = $1, description = $2, tech_stack = $3, image_url = $4, 
          project_url = $5, github_url = $6, updated_at = CURRENT_TIMESTAMP 
      WHERE id = $7 
      RETURNING *
    `;
    const result = await pool.query(query, [title, description, tech_stack, image_url, project_url, github_url, id]);
    return result.rows[0];
  },

  // Delete project
  delete: async (id) => {
    const query = 'DELETE FROM projects WHERE id = $1 RETURNING *';
    const result = await pool.query(query, [id]);
    return result.rows[0];
  }
};

// Testimonials CRUD operations
const testimonialsService = {
  // Get all testimonials
  getAll: async () => {
    const query = 'SELECT * FROM testimonials ORDER BY created_at DESC';
    const result = await pool.query(query);
    return result.rows;
  },

  // Get testimonial by ID
  getById: async (id) => {
    const query = 'SELECT * FROM testimonials WHERE id = $1';
    const result = await pool.query(query, [id]);
    return result.rows[0];
  },

  // Create new testimonial
  create: async (testimonialData) => {
    const { quote, client_name, client_position, company, rating } = testimonialData;
    const query = `
      INSERT INTO testimonials (quote, client_name, client_position, company, rating) 
      VALUES ($1, $2, $3, $4, $5) 
      RETURNING *
    `;
    const result = await pool.query(query, [quote, client_name, client_position, company, rating || 5]);
    return result.rows[0];
  },

  // Update testimonial
  update: async (id, testimonialData) => {
    const { quote, client_name, client_position, company, rating } = testimonialData;
    const query = `
      UPDATE testimonials 
      SET quote = $1, client_name = $2, client_position = $3, company = $4, 
          rating = $5, updated_at = CURRENT_TIMESTAMP 
      WHERE id = $6 
      RETURNING *
    `;
    const result = await pool.query(query, [quote, client_name, client_position, company, rating, id]);
    return result.rows[0];
  },

  // Delete testimonial
  delete: async (id) => {
    const query = 'DELETE FROM testimonials WHERE id = $1 RETURNING *';
    const result = await pool.query(query, [id]);
    return result.rows[0];
  }
};

module.exports = {
  servicesService,
  projectsService,
  testimonialsService
};
