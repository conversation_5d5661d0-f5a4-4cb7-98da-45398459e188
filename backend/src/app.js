const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const { testConnection } = require('./config/database');
const { initializeTables } = require('./models/index');
const { createAdminTable } = require('./middleware/auth');
const routes = require('./routes/index');

// Load environment variables
dotenv.config();

const app = express();

// Middleware - CORS configuration for local development
app.use(cors({
  origin: function (origin, callback) {
    console.log('CORS Origin:', origin); // Debug logging

    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) {
      console.log('CORS: Allowing request with no origin');
      return callback(null, true);
    }

    // Allow file:// origins for local development
    if (origin.startsWith('file://')) {
      console.log('CORS: Allowing file:// origin');
      return callback(null, true);
    }

    // Allow localhost origins
    if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
      console.log('CORS: Allowing localhost origin');
      return callback(null, true);
    }

    // Allow specific origins
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:8080',
      'http://127.0.0.1:3000',
      process.env.FRONTEND_URL
    ].filter(Boolean);

    if (allowedOrigins.includes(origin)) {
      console.log('CORS: Allowing whitelisted origin');
      return callback(null, true);
    }

    console.log('CORS: Rejecting origin:', origin);
    callback(new Error('Not allowed by CORS'));
  },
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    service: 'Eclipse Softworks Backend'
  });
});

// API Routes
app.use('/api', routes);

// Root route - Welcome message
app.get('/', (req, res) => {
  res.status(200).json({
    message: 'Welcome to Eclipse Softworks API',
    service: 'Eclipse Softworks Backend',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      api: '/api',
      services: '/api/services',
      portfolio: '/api/portfolio',
      testimonials: '/api/testimonials',
      auth: '/api/auth'
    },
    frontend: {
      website: 'Open frontend/index.html in your browser',
      admin: 'Open frontend/admin.html for admin panel'
    },
    documentation: 'See ECLIPSE_SYSTEM_OVERVIEW.md for complete system info'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production'
      ? 'Internal server error'
      : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// Initialize database and start server
const startServer = async () => {
  try {
    // Start server first
    const PORT = process.env.PORT || 5000;
    const server = app.listen(PORT, () => {
      console.log(`🚀 Server is running on port ${PORT}`);
      console.log(`📍 Health check: http://localhost:${PORT}/health`);
      console.log(`🔗 API base URL: http://localhost:${PORT}/api`);
    });

    // Test database connection (non-blocking)
    console.log('🔍 Testing database connection...');
    const dbConnected = await testConnection();

    if (dbConnected) {
      // Initialize database tables
      try {
        await initializeTables();
        await createAdminTable();
        console.log('✅ Database initialized successfully');
      } catch (error) {
        console.error('⚠️  Database initialization failed:', error.message);
        console.log('🔄 Server will continue running, but database operations may fail');
      }
    } else {
      console.log('⚠️  Database connection failed - server running in limited mode');
      console.log('🔄 Database operations will not be available until connection is restored');
    }

    return server;
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();