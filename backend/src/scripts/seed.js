const { pool } = require('../config/database');
const { hashPassword } = require('../middleware/auth');

// Sample data
const sampleServices = [
  {
    title: 'Web Development',
    description: 'Custom web applications built with modern technologies like React, Node.js, and PostgreSQL.',
    icon: 'fas fa-code'
  },
  {
    title: 'Mobile App Development',
    description: 'Native and cross-platform mobile applications for iOS and Android using React Native and Flutter.',
    icon: 'fas fa-mobile-alt'
  },
  {
    title: 'UI/UX Design',
    description: 'User-centered design solutions that create intuitive and engaging digital experiences.',
    icon: 'fas fa-paint-brush'
  },
  {
    title: 'Cloud Solutions',
    description: 'Scalable cloud infrastructure and deployment solutions using AWS, Azure, and Google Cloud.',
    icon: 'fas fa-cloud'
  }
];

const sampleProjects = [
  {
    title: 'E-Commerce Platform',
    description: 'A full-stack e-commerce solution with payment integration, inventory management, and admin dashboard.',
    tech_stack: 'React, Node.js, PostgreSQL, Stripe API',
    image_url: 'https://via.placeholder.com/600x400/4f46e5/ffffff?text=E-Commerce+Platform',
    project_url: 'https://example-ecommerce.com',
    github_url: 'https://github.com/eclipse-softworks/ecommerce-platform'
  },
  {
    title: 'Task Management App',
    description: 'A collaborative task management application with real-time updates and team collaboration features.',
    tech_stack: 'React Native, Express.js, MongoDB, Socket.io',
    image_url: 'https://via.placeholder.com/600x400/059669/ffffff?text=Task+Management+App',
    project_url: 'https://example-taskapp.com',
    github_url: 'https://github.com/eclipse-softworks/task-management'
  },
  {
    title: 'Analytics Dashboard',
    description: 'A comprehensive analytics dashboard with data visualization and reporting capabilities.',
    tech_stack: 'Vue.js, Python, FastAPI, PostgreSQL, Chart.js',
    image_url: 'https://via.placeholder.com/600x400/dc2626/ffffff?text=Analytics+Dashboard',
    project_url: 'https://example-analytics.com',
    github_url: 'https://github.com/eclipse-softworks/analytics-dashboard'
  }
];

const sampleTestimonials = [
  {
    quote: 'Eclipse Softworks delivered an exceptional web application that exceeded our expectations. Their attention to detail and technical expertise is outstanding.',
    client_name: 'Sarah Johnson',
    client_position: 'CEO',
    company: 'TechStart Inc.',
    rating: 5
  },
  {
    quote: 'The mobile app they developed for us has significantly improved our customer engagement. Highly professional team with great communication.',
    client_name: 'Michael Chen',
    client_position: 'Product Manager',
    company: 'InnovateCorp',
    rating: 5
  },
  {
    quote: 'Working with Eclipse Softworks was a pleasure. They understood our requirements perfectly and delivered a robust solution on time.',
    client_name: 'Emily Rodriguez',
    client_position: 'CTO',
    company: 'DataFlow Solutions',
    rating: 5
  }
];

// Seed functions
const seedServices = async () => {
  console.log('🌱 Seeding services...');
  
  for (const service of sampleServices) {
    const query = `
      INSERT INTO services (title, description, icon) 
      VALUES ($1, $2, $3) 
      ON CONFLICT DO NOTHING
    `;
    await pool.query(query, [service.title, service.description, service.icon]);
  }
  
  console.log('✅ Services seeded successfully');
};

const seedProjects = async () => {
  console.log('🌱 Seeding projects...');
  
  for (const project of sampleProjects) {
    const query = `
      INSERT INTO projects (title, description, tech_stack, image_url, project_url, github_url) 
      VALUES ($1, $2, $3, $4, $5, $6) 
      ON CONFLICT DO NOTHING
    `;
    await pool.query(query, [
      project.title, 
      project.description, 
      project.tech_stack, 
      project.image_url, 
      project.project_url, 
      project.github_url
    ]);
  }
  
  console.log('✅ Projects seeded successfully');
};

const seedTestimonials = async () => {
  console.log('🌱 Seeding testimonials...');
  
  for (const testimonial of sampleTestimonials) {
    const query = `
      INSERT INTO testimonials (quote, client_name, client_position, company, rating) 
      VALUES ($1, $2, $3, $4, $5) 
      ON CONFLICT DO NOTHING
    `;
    await pool.query(query, [
      testimonial.quote, 
      testimonial.client_name, 
      testimonial.client_position, 
      testimonial.company, 
      testimonial.rating
    ]);
  }
  
  console.log('✅ Testimonials seeded successfully');
};

const seedAdminUser = async () => {
  console.log('🌱 Creating default admin user...');
  
  // Check if admin user already exists
  const checkQuery = 'SELECT COUNT(*) FROM admin_users';
  const checkResult = await pool.query(checkQuery);
  const adminCount = parseInt(checkResult.rows[0].count);
  
  if (adminCount === 0) {
    const defaultPassword = 'Admin123!';
    const passwordHash = await hashPassword(defaultPassword);
    
    const query = `
      INSERT INTO admin_users (username, email, password_hash, role) 
      VALUES ($1, $2, $3, 'admin')
    `;
    await pool.query(query, ['admin', '<EMAIL>', passwordHash]);
    
    console.log('✅ Default admin user created');
    console.log('📧 Username: admin');
    console.log('🔑 Password: Admin123!');
    console.log('⚠️  Please change the default password after first login!');
  } else {
    console.log('ℹ️  Admin user already exists, skipping...');
  }
};

// Main seed function
const seedDatabase = async () => {
  try {
    console.log('🚀 Starting database seeding...');
    
    await seedServices();
    await seedProjects();
    await seedTestimonials();
    await seedAdminUser();
    
    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding process failed:', error);
      process.exit(1);
    });
}

module.exports = { seedDatabase };
