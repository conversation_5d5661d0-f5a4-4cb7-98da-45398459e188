const { pool } = require('../config/database');

const realServices = [
  {
    title: "Custom Web Development",
    description: "We build modern, responsive websites and web applications tailored to your business needs. From simple landing pages to complex e-commerce platforms, we deliver solutions that drive results.",
    icon: "fas fa-code"
  },
  {
    title: "Mobile App Development", 
    description: "Native and cross-platform mobile applications for iOS and Android. We create user-friendly apps that engage your customers and grow your business on mobile devices.",
    icon: "fas fa-mobile-alt"
  },
  {
    title: "E-Commerce Solutions",
    description: "Complete online store development with secure payment processing, inventory management, and customer analytics. Turn your products into profitable online sales.",
    icon: "fas fa-shopping-cart"
  },
  {
    title: "Digital Marketing & SEO",
    description: "Boost your online presence with our comprehensive digital marketing services. SEO optimization, social media management, and targeted advertising campaigns.",
    icon: "fas fa-chart-line"
  },
  {
    title: "Cloud Solutions & Hosting",
    description: "Reliable cloud infrastructure and hosting services. We ensure your applications are fast, secure, and always available with 99.9% uptime guarantee.",
    icon: "fas fa-cloud"
  },
  {
    title: "Technical Consulting",
    description: "Expert technology consulting to help you make informed decisions about your digital transformation. Architecture planning, technology stack selection, and project roadmaps.",
    icon: "fas fa-lightbulb"
  }
];

const realProjects = [
  {
    title: "Restaurant Management System",
    description: "A comprehensive restaurant management platform with online ordering, table reservations, inventory tracking, and staff management. Built for a local restaurant chain.",
    tech_stack: "React, Node.js, PostgreSQL, Stripe API, Socket.io",
    image_url: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=600&h=400&fit=crop",
    project_url: "https://demo-restaurant.eclipsesoftworks.com",
    github_url: "https://github.com/eclipse-softworks/restaurant-management"
  },
  {
    title: "Healthcare Appointment System",
    description: "Digital appointment booking system for medical practices with patient records, automated reminders, and telehealth integration. Serving 5+ clinics.",
    tech_stack: "Vue.js, Express.js, MongoDB, Twilio API, WebRTC",
    image_url: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop",
    project_url: "https://healthbook.eclipsesoftworks.com",
    github_url: "https://github.com/eclipse-softworks/healthcare-booking"
  },
  {
    title: "Real Estate Platform",
    description: "Modern property listing and management platform with virtual tours, mortgage calculators, and agent dashboards. Connecting buyers with their dream homes.",
    tech_stack: "Next.js, Python Django, PostgreSQL, AWS S3, Mapbox",
    image_url: "https://images.unsplash.com/photo-**********-ce09059eeffa?w=600&h=400&fit=crop",
    project_url: "https://properties.eclipsesoftworks.com",
    github_url: "https://github.com/eclipse-softworks/real-estate-platform"
  },
  {
    title: "Educational Learning Management",
    description: "Interactive online learning platform with course creation tools, progress tracking, and student-teacher communication. Empowering education in the digital age.",
    tech_stack: "React Native, Laravel, MySQL, Redis, FFmpeg",
    image_url: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop",
    project_url: "https://edulearn.eclipsesoftworks.com",
    github_url: "https://github.com/eclipse-softworks/education-platform"
  },
  {
    title: "Inventory Management System",
    description: "Smart inventory tracking system with barcode scanning, automated reordering, and analytics dashboard. Helping businesses optimize their supply chain.",
    tech_stack: "Angular, .NET Core, SQL Server, Azure, Power BI",
    image_url: "https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=600&h=400&fit=crop",
    project_url: "https://inventory.eclipsesoftworks.com",
    github_url: "https://github.com/eclipse-softworks/inventory-system"
  }
];

const realTestimonials = [
  {
    quote: "Eclipse Softworks transformed our restaurant operations completely. The online ordering system increased our sales by 40% and the staff management features saved us hours every week. Exceptional work!",
    client_name: "Marco Rossi",
    client_position: "Owner",
    company: "Bella Vista Restaurant Group",
    rating: 5
  },
  {
    quote: "Working with Eclipse Softworks was a game-changer for our medical practice. The appointment system reduced no-shows by 60% and our patients love the convenience. Professional team with great support.",
    client_name: "Dr. Sarah Mitchell",
    client_position: "Practice Manager",
    company: "Newcastle Medical Center",
    rating: 5
  },
  {
    quote: "The real estate platform they built for us is outstanding. Our agents can now manage listings efficiently and clients love the virtual tour feature. Sales have increased significantly since launch.",
    client_name: "James Thompson",
    client_position: "CEO",
    company: "Premier Properties SA",
    rating: 5
  },
  {
    quote: "Eclipse Softworks delivered exactly what we needed for our online courses. The platform is intuitive, scalable, and our students are more engaged than ever. Highly recommend their services!",
    client_name: "Prof. Linda Adams",
    client_position: "Director of Digital Learning",
    company: "Skills Academy",
    rating: 5
  },
  {
    quote: "Our inventory management was a nightmare before Eclipse Softworks stepped in. Now everything is automated, accurate, and we have real-time insights. They exceeded our expectations in every way.",
    client_name: "David Chen",
    client_position: "Operations Manager",
    company: "TechSupply Solutions",
    rating: 5
  }
];

async function updateRealData() {
  try {
    console.log('🔄 Updating database with real Eclipse Softworks data...');

    // Clear existing data
    console.log('🗑️  Clearing existing sample data...');
    await pool.query('DELETE FROM services WHERE id > 0');
    await pool.query('DELETE FROM projects WHERE id > 0');
    await pool.query('DELETE FROM testimonials WHERE id > 0');

    // Reset sequences
    await pool.query('ALTER SEQUENCE services_id_seq RESTART WITH 1');
    await pool.query('ALTER SEQUENCE projects_id_seq RESTART WITH 1');
    await pool.query('ALTER SEQUENCE testimonials_id_seq RESTART WITH 1');

    // Insert real services
    console.log('🛠️  Adding real services...');
    for (const service of realServices) {
      await pool.query(
        'INSERT INTO services (title, description, icon) VALUES ($1, $2, $3)',
        [service.title, service.description, service.icon]
      );
    }

    // Insert real projects
    console.log('💼 Adding real projects...');
    for (const project of realProjects) {
      await pool.query(
        'INSERT INTO projects (title, description, tech_stack, image_url, project_url, github_url) VALUES ($1, $2, $3, $4, $5, $6)',
        [project.title, project.description, project.tech_stack, project.image_url, project.project_url, project.github_url]
      );
    }

    // Insert real testimonials
    console.log('💬 Adding real testimonials...');
    for (const testimonial of realTestimonials) {
      await pool.query(
        'INSERT INTO testimonials (quote, client_name, client_position, company, rating) VALUES ($1, $2, $3, $4, $5)',
        [testimonial.quote, testimonial.client_name, testimonial.client_position, testimonial.company, testimonial.rating]
      );
    }

    console.log('✅ Real data update completed successfully!');
    console.log(`📊 Added ${realServices.length} services`);
    console.log(`📊 Added ${realProjects.length} projects`);
    console.log(`📊 Added ${realTestimonials.length} testimonials`);

  } catch (error) {
    console.error('❌ Error updating real data:', error);
    throw error;
  }
}

// Run the update if this script is executed directly
if (require.main === module) {
  updateRealData()
    .then(() => {
      console.log('🎉 Database updated with real Eclipse Softworks data!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Failed to update database:', error);
      process.exit(1);
    });
}

module.exports = { updateRealData };
