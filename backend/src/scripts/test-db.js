const { Pool } = require('pg');
require('dotenv').config();

const testDatabaseConnection = async () => {
  console.log('🔍 Testing database connection...');
  console.log('📍 Database URL:', process.env.POSTGRES_URL ? 'Set' : 'Not set');
  
  if (!process.env.POSTGRES_URL) {
    console.log('❌ POSTGRES_URL environment variable is not set');
    return false;
  }

  const pool = new Pool({
    connectionString: process.env.POSTGRES_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    connectionTimeoutMillis: 5000, // 5 second timeout
  });

  try {
    console.log('🔗 Attempting to connect...');
    const client = await pool.connect();
    
    console.log('✅ Connection successful!');
    
    // Test a simple query
    const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');
    console.log('📅 Current time:', result.rows[0].current_time);
    console.log('🐘 PostgreSQL version:', result.rows[0].postgres_version);
    
    client.release();
    await pool.end();
    
    return true;
  } catch (error) {
    console.log('❌ Connection failed:', error.message);
    
    if (error.code) {
      console.log('🔍 Error code:', error.code);
    }
    
    if (error.message.includes('timeout')) {
      console.log('⏰ This appears to be a timeout issue. Possible causes:');
      console.log('   - Network connectivity issues');
      console.log('   - Database server is down');
      console.log('   - Firewall blocking the connection');
      console.log('   - Incorrect connection string');
    }
    
    await pool.end();
    return false;
  }
};

// Run the test
testDatabaseConnection()
  .then((success) => {
    if (success) {
      console.log('🎉 Database connection test passed!');
      process.exit(0);
    } else {
      console.log('💡 Suggestions:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify the POSTGRES_URL in your .env file');
      console.log('   3. Ensure the database server is running');
      console.log('   4. Try connecting from a different network');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
