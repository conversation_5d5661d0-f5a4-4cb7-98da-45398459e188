const { servicesService, projectsService, testimonialsService, contactService } = require('../services/database');
const { validationResult } = require('express-validator');

// Helper function to handle async errors
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Helper function to handle validation errors
const handleValidationErrors = (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  return null;
};

// Services Controllers
const getServices = asyncHandler(async (req, res) => {
  const services = await servicesService.getAll();
  res.status(200).json({
    success: true,
    count: services.length,
    data: services
  });
});

const getServiceById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const service = await servicesService.getById(id);

  if (!service) {
    return res.status(404).json({
      success: false,
      error: 'Service not found'
    });
  }

  res.status(200).json({
    success: true,
    data: service
  });
});

const createService = asyncHandler(async (req, res) => {
  const validationError = handleValidationErrors(req, res);
  if (validationError) return validationError;

  const service = await servicesService.create(req.body);
  res.status(201).json({
    success: true,
    data: service
  });
});

const updateService = asyncHandler(async (req, res) => {
  const validationError = handleValidationErrors(req, res);
  if (validationError) return validationError;

  const { id } = req.params;
  const service = await servicesService.update(id, req.body);

  if (!service) {
    return res.status(404).json({
      success: false,
      error: 'Service not found'
    });
  }

  res.status(200).json({
    success: true,
    data: service
  });
});

const deleteService = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const service = await servicesService.delete(id);

  if (!service) {
    return res.status(404).json({
      success: false,
      error: 'Service not found'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Service deleted successfully'
  });
});

// Portfolio (Projects) Controllers
const getPortfolio = asyncHandler(async (req, res) => {
  const projects = await projectsService.getAll();
  res.status(200).json({
    success: true,
    count: projects.length,
    data: projects
  });
});

const getProjectById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const project = await projectsService.getById(id);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  res.status(200).json({
    success: true,
    data: project
  });
});

const createProject = asyncHandler(async (req, res) => {
  const validationError = handleValidationErrors(req, res);
  if (validationError) return validationError;

  const project = await projectsService.create(req.body);
  res.status(201).json({
    success: true,
    data: project
  });
});

const updateProject = asyncHandler(async (req, res) => {
  const validationError = handleValidationErrors(req, res);
  if (validationError) return validationError;

  const { id } = req.params;
  const project = await projectsService.update(id, req.body);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  res.status(200).json({
    success: true,
    data: project
  });
});

const deleteProject = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const project = await projectsService.delete(id);

  if (!project) {
    return res.status(404).json({
      success: false,
      error: 'Project not found'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Project deleted successfully'
  });
});

// Testimonials Controllers
const getTestimonials = asyncHandler(async (req, res) => {
  const testimonials = await testimonialsService.getAll();
  res.status(200).json({
    success: true,
    count: testimonials.length,
    data: testimonials
  });
});

const getTestimonialById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const testimonial = await testimonialsService.getById(id);

  if (!testimonial) {
    return res.status(404).json({
      success: false,
      error: 'Testimonial not found'
    });
  }

  res.status(200).json({
    success: true,
    data: testimonial
  });
});

const createTestimonial = asyncHandler(async (req, res) => {
  const validationError = handleValidationErrors(req, res);
  if (validationError) return validationError;

  const testimonial = await testimonialsService.create(req.body);
  res.status(201).json({
    success: true,
    data: testimonial
  });
});

const updateTestimonial = asyncHandler(async (req, res) => {
  const validationError = handleValidationErrors(req, res);
  if (validationError) return validationError;

  const { id } = req.params;
  const testimonial = await testimonialsService.update(id, req.body);

  if (!testimonial) {
    return res.status(404).json({
      success: false,
      error: 'Testimonial not found'
    });
  }

  res.status(200).json({
    success: true,
    data: testimonial
  });
});

const deleteTestimonial = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const testimonial = await testimonialsService.delete(id);

  if (!testimonial) {
    return res.status(404).json({
      success: false,
      error: 'Testimonial not found'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Testimonial deleted successfully'
  });
});

// Contact Messages Controllers
const getContactMessages = asyncHandler(async (req, res) => {
  const messages = await contactService.getAll();
  res.status(200).json({
    success: true,
    data: messages
  });
});

const getContactMessageById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const message = await contactService.getById(id);

  if (!message) {
    return res.status(404).json({
      success: false,
      error: 'Contact message not found'
    });
  }

  res.status(200).json({
    success: true,
    data: message
  });
});

const createContactMessage = asyncHandler(async (req, res) => {
  const validationError = handleValidationErrors(req, res);
  if (validationError) return validationError;

  const message = await contactService.create(req.body);
  res.status(201).json({
    success: true,
    data: message,
    message: 'Thank you for your message! We will get back to you soon.'
  });
});

const updateContactMessageStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  if (!status || !['new', 'read', 'replied', 'archived'].includes(status)) {
    return res.status(400).json({
      success: false,
      error: 'Valid status is required (new, read, replied, archived)'
    });
  }

  const message = await contactService.updateStatus(id, status);

  if (!message) {
    return res.status(404).json({
      success: false,
      error: 'Contact message not found'
    });
  }

  res.status(200).json({
    success: true,
    data: message
  });
});

const deleteContactMessage = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const message = await contactService.delete(id);

  if (!message) {
    return res.status(404).json({
      success: false,
      error: 'Contact message not found'
    });
  }

  res.status(200).json({
    success: true,
    message: 'Contact message deleted successfully'
  });
});

module.exports = {
  // Services
  getServices,
  getServiceById,
  createService,
  updateService,
  deleteService,

  // Portfolio/Projects
  getPortfolio,
  getProjectById,
  createProject,
  updateProject,
  deleteProject,

  // Testimonials
  getTestimonials,
  getTestimonialById,
  createTestimonial,
  updateTestimonial,
  deleteTestimonial,

  // Contact Messages
  getContactMessages,
  getContactMessageById,
  createContactMessage,
  updateContactMessageStatus,
  deleteContactMessage
};