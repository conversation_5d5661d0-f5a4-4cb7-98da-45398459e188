import mongoose from 'mongoose';

const serviceSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    icon: {
        type: String,
        required: true
    }
});

const projectSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    techStack: {
        type: String,
        required: true
    },
    imageUrl: {
        type: String,
        required: true
    }
});

const testimonialSchema = new mongoose.Schema({
    quote: {
        type: String,
        required: true
    },
    clientName: {
        type: String,
        required: true
    },
    clientPosition: {
        type: String,
        required: true
    }
});

const Service = mongoose.model('Service', serviceSchema);
const Project = mongoose.model('Project', projectSchema);
const Testimonial = mongoose.model('Testimonial', testimonialSchema);

export { Service, Project, Testimonial };