const express = require('express');
const router = express.Router();
const {
  // Services
  getServices,
  getServiceById,
  createService,
  updateService,
  deleteService,

  // Portfolio/Projects
  getPortfolio,
  getProjectById,
  createProject,
  updateProject,
  deleteProject,

  // Testimonials
  getTestimonials,
  getTestimonialById,
  createTestimonial,
  updateTestimonial,
  deleteTestimonial,

  // Contact Messages
  getContactMessages,
  getContactMessageById,
  createContactMessage,
  updateContactMessageStatus,
  deleteContactMessage
} = require('../controllers/index');

const {
  idValidation,
  serviceValidation,
  projectValidation,
  testimonialValidation,
  contactValidation
} = require('../middleware/validation');

const { authenticateToken, requireAdmin } = require('../middleware/auth');
const authRoutes = require('./auth');

// Authentication routes
router.use('/auth', authRoutes);

// Services Routes (GET routes are public, CUD routes require admin)
router.get('/services', getServices);
router.get('/services/:id', idValidation, getServiceById);
router.post('/services', authenticateToken, requireAdmin, serviceValidation, createService);
router.put('/services/:id', authenticateToken, requireAdmin, [...idValidation, ...serviceValidation], updateService);
router.delete('/services/:id', authenticateToken, requireAdmin, idValidation, deleteService);

// Portfolio/Projects Routes (GET routes are public, CUD routes require admin)
router.get('/portfolio', getPortfolio);
router.get('/portfolio/:id', idValidation, getProjectById);
router.post('/portfolio', authenticateToken, requireAdmin, projectValidation, createProject);
router.put('/portfolio/:id', authenticateToken, requireAdmin, [...idValidation, ...projectValidation], updateProject);
router.delete('/portfolio/:id', authenticateToken, requireAdmin, idValidation, deleteProject);

// Testimonials Routes (GET routes are public, CUD routes require admin)
router.get('/testimonials', getTestimonials);
router.get('/testimonials/:id', idValidation, getTestimonialById);
router.post('/testimonials', authenticateToken, requireAdmin, testimonialValidation, createTestimonial);
router.put('/testimonials/:id', authenticateToken, requireAdmin, [...idValidation, ...testimonialValidation], updateTestimonial);
router.delete('/testimonials/:id', authenticateToken, requireAdmin, idValidation, deleteTestimonial);

// Contact Messages Routes (POST is public, GET/PUT/DELETE require admin)
router.post('/contact', contactValidation, createContactMessage);
router.get('/contact', authenticateToken, requireAdmin, getContactMessages);
router.get('/contact/:id', authenticateToken, requireAdmin, idValidation, getContactMessageById);
router.put('/contact/:id/status', authenticateToken, requireAdmin, idValidation, updateContactMessageStatus);
router.delete('/contact/:id', authenticateToken, requireAdmin, idValidation, deleteContactMessage);

module.exports = router;