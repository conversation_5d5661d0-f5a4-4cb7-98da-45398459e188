const { body, param } = require('express-validator');

// Common validation rules
const idValidation = [
  param('id').isInt({ min: 1 }).withMessage('ID must be a positive integer')
];

// Service validation rules
const serviceValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title is required and must be between 1 and 255 characters'),
  body('description')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Description is required and must be between 1 and 1000 characters'),
  body('icon')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Icon is required and must be between 1 and 255 characters')
];

// Project validation rules
const projectValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Title is required and must be between 1 and 255 characters'),
  body('description')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Description is required and must be between 1 and 1000 characters'),
  body('tech_stack')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Tech stack is required and must be between 1 and 500 characters'),
  body('image_url')
    .trim()
    .isURL()
    .withMessage('Image URL must be a valid URL'),
  body('project_url')
    .optional()
    .trim()
    .isURL()
    .withMessage('Project URL must be a valid URL'),
  body('github_url')
    .optional()
    .trim()
    .isURL()
    .withMessage('GitHub URL must be a valid URL')
];

// Testimonial validation rules
const testimonialValidation = [
  body('quote')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Quote is required and must be between 1 and 1000 characters'),
  body('client_name')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Client name is required and must be between 1 and 255 characters'),
  body('client_position')
    .trim()
    .isLength({ min: 1, max: 255 })
    .withMessage('Client position is required and must be between 1 and 255 characters'),
  body('company')
    .optional()
    .trim()
    .isLength({ max: 255 })
    .withMessage('Company name must be less than 255 characters'),
  body('rating')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be an integer between 1 and 5')
];

module.exports = {
  idValidation,
  serviceValidation,
  projectValidation,
  testimonialValidation
};
