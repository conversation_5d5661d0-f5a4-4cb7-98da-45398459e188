# Eclipse Softworks Backend

## Overview
This project is the backend for the Eclipse Softworks website. It is built using Node.js and Express, providing a RESTful API to support the frontend application.

## Project Structure
```
eclipse-softworks-backend
├── src
│   ├── app.js                # Entry point of the application
│   ├── controllers           # Contains controller functions for handling requests
│   ├── routes                # Defines the routes for the application
│   ├── models                # Contains data models for the application
│   └── config                # Configuration files, including database connection
├── package.json              # npm configuration file
├── .env                      # Environment variables
└── README.md                 # Project documentation
```

## Installation
1. Clone the repository:
   ```
   git clone <repository-url>
   ```
2. Navigate to the project directory:
   ```
   cd eclipse-softworks-backend
   ```
3. Install the dependencies:
   ```
   npm install
   ```

## Configuration
1. Create a `.env` file in the root directory and add your environment variables. Example:
   ```
   DATABASE_URL=mongodb://localhost:27017/eclipse-softworks
   PORT=5000
   ```

## Usage
To start the server, run:
```
npm start
```
The server will run on the specified port (default is 5000).

## API Endpoints
- **GET /services**: Retrieve a list of services offered.
- **GET /portfolio**: Retrieve the portfolio of projects.
- **GET /testimonials**: Retrieve client testimonials.

## Contributing
Contributions are welcome! Please submit a pull request or open an issue for any suggestions or improvements.

## License
This project is licensed under the MIT License. See the LICENSE file for details.