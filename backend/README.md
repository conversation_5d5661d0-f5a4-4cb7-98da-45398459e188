# Eclipse Softworks Backend

A robust Node.js/Express backend API for the Eclipse Softworks website with PostgreSQL database, JWT authentication, and comprehensive CRUD operations.

## 🚀 Features

- **RESTful API** with Express.js
- **PostgreSQL Database** with connection pooling
- **JWT Authentication** for admin operations
- **Input Validation** with express-validator
- **CORS Support** for frontend integration
- **E<PERSON>r Handling** with comprehensive error responses
- **Database Seeding** with sample data
- **Secure Password Hashing** with bcryptjs

## 📋 API Endpoints

### Authentication
- `POST /api/auth/login` - Admin login
- `POST /api/auth/setup-admin` - Create initial admin user (one-time only)

### Services (Public GET, Admin CUD)
- `GET /api/services` - Get all services
- `GET /api/services/:id` - Get service by ID
- `POST /api/services` - Create new service (Admin only)
- `PUT /api/services/:id` - Update service (Admin only)
- `DELETE /api/services/:id` - Delete service (Admin only)

### Portfolio/Projects (Public GET, Admin CUD)
- `GET /api/portfolio` - Get all projects
- `GET /api/portfolio/:id` - Get project by ID
- `POST /api/portfolio` - Create new project (Admin only)
- `PUT /api/portfolio/:id` - Update project (Admin only)
- `DELETE /api/portfolio/:id` - Delete project (Admin only)

### Testimonials (Public GET, Admin CUD)
- `GET /api/testimonials` - Get all testimonials
- `GET /api/testimonials/:id` - Get testimonial by ID
- `POST /api/testimonials` - Create new testimonial (Admin only)
- `PUT /api/testimonials/:id` - Update testimonial (Admin only)
- `DELETE /api/testimonials/:id` - Delete testimonial (Admin only)

### Health Check
- `GET /health` - Server health status

## 🛠️ Installation & Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Environment Setup:**
   - Copy `.env.example` to `.env` (if available)
   - Update the environment variables in `.env`:
     ```env
     POSTGRES_URL=your_postgresql_connection_string
     PORT=3000
     JWT_SECRET=your_secure_jwt_secret
     NODE_ENV=development
     FRONTEND_URL=http://localhost:3000
     ```

3. **Database Setup:**
   - Ensure PostgreSQL is running
   - The application will automatically create tables on startup

4. **Seed Database (Optional):**
   ```bash
   npm run seed
   ```
   This creates sample data and a default admin user:
   - Username: `admin`
   - Password: `Admin123!`
   - **⚠️ Change the default password after first login!**

## 🏃‍♂️ Running the Application

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The server will start on the port specified in your `.env` file (default: 3000).

## 📊 Database Schema

### Services Table
- `id` (SERIAL PRIMARY KEY)
- `title` (VARCHAR(255) NOT NULL)
- `description` (TEXT NOT NULL)
- `icon` (VARCHAR(255) NOT NULL)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Projects Table
- `id` (SERIAL PRIMARY KEY)
- `title` (VARCHAR(255) NOT NULL)
- `description` (TEXT NOT NULL)
- `tech_stack` (VARCHAR(500) NOT NULL)
- `image_url` (VARCHAR(500) NOT NULL)
- `project_url` (VARCHAR(500))
- `github_url` (VARCHAR(500))
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Testimonials Table
- `id` (SERIAL PRIMARY KEY)
- `quote` (TEXT NOT NULL)
- `client_name` (VARCHAR(255) NOT NULL)
- `client_position` (VARCHAR(255) NOT NULL)
- `company` (VARCHAR(255))
- `rating` (INTEGER DEFAULT 5)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

### Admin Users Table
- `id` (SERIAL PRIMARY KEY)
- `username` (VARCHAR(255) UNIQUE NOT NULL)
- `email` (VARCHAR(255) UNIQUE NOT NULL)
- `password_hash` (VARCHAR(255) NOT NULL)
- `role` (VARCHAR(50) DEFAULT 'admin')
- `is_active` (BOOLEAN DEFAULT true)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication. Admin endpoints require a valid JWT token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

To get a token, use the login endpoint with admin credentials.

## 📝 API Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "count": 10 // (for list endpoints)
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "details": [ ... ] // (for validation errors)
}
```

## 🧪 Testing

You can test the API endpoints using tools like:
- **Postman** - Import the API collection
- **curl** - Command line testing
- **Thunder Client** - VS Code extension

Example curl request:
```bash
# Get all services
curl http://localhost:3000/api/services

# Login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "Admin123!"}'
```

## 🔧 Project Structure

```
backend/
├── src/
│   ├── config/
│   │   └── database.js          # Database connection
│   ├── controllers/
│   │   └── index.js             # API controllers
│   ├── middleware/
│   │   ├── auth.js              # Authentication middleware
│   │   └── validation.js        # Input validation rules
│   ├── models/
│   │   └── index.js             # Database models/schemas
│   ├── routes/
│   │   ├── auth.js              # Authentication routes
│   │   └── index.js             # Main API routes
│   ├── scripts/
│   │   └── seed.js              # Database seeding script
│   ├── services/
│   │   └── database.js          # Database service functions
│   └── app.js                   # Main application file
├── .env                         # Environment variables
├── package.json                 # Dependencies and scripts
└── README.md                    # This file
```

## 🚀 Deployment

1. **Environment Variables:** Set production environment variables
2. **Database:** Ensure PostgreSQL is accessible
3. **JWT Secret:** Use a strong, unique JWT secret in production
4. **CORS:** Update FRONTEND_URL for your production domain
5. **SSL:** Enable SSL for production database connections

## 📄 License

MIT License - see LICENSE file for details.

## 👥 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support, please contact Eclipse Softworks or create an issue in the repository.