<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test - Eclipse Softworks</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ccc;
        }
        .success {
            background-color: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Eclipse Softworks API Connection Test</h1>
        <p>This page tests the connection between the frontend and backend API.</p>
        
        <div class="test-result info">
            <strong>API Base URL:</strong> <span id="api-url">http://localhost:5000/api</span>
        </div>
        
        <div id="results"></div>
        
        <button onclick="testConnection()">🔌 Test API Connection</button>
        <button onclick="testServices()">🛠️ Test Services Endpoint</button>
        <button onclick="testLogin()">🔐 Test Login</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000/api';
        const resultsDiv = document.getElementById('results');

        function addResult(type, title, content) {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${title}</strong><br>${content}`;
            resultsDiv.appendChild(div);
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testConnection() {
            addResult('info', '🔌 Testing API Connection...', 'Attempting to connect to the backend server...');
            
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                
                if (response.ok) {
                    addResult('success', '✅ Connection Successful!', 
                        `Server is running and healthy.<br><pre>${JSON.stringify(data, null, 2)}</pre>`);
                } else {
                    addResult('error', '❌ Connection Failed', 
                        `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addResult('error', '❌ Network Error', 
                    `Failed to connect to server: ${error.message}<br><br>
                    <strong>Possible causes:</strong><br>
                    • Backend server is not running<br>
                    • CORS configuration issue<br>
                    • Network connectivity problem`);
            }
        }

        async function testServices() {
            addResult('info', '🛠️ Testing Services Endpoint...', 'Fetching services data from API...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/services`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('success', '✅ Services API Working!', 
                        `Found ${data.count} services.<br><pre>${JSON.stringify(data, null, 2)}</pre>`);
                } else {
                    addResult('error', '❌ Services API Failed', 
                        `HTTP ${response.status}: ${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                addResult('error', '❌ Services API Error', 
                    `Failed to fetch services: ${error.message}`);
            }
        }

        async function testLogin() {
            addResult('info', '🔐 Testing Login Endpoint...', 'Attempting admin login...');
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'NewSecurePassword123!'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    addResult('success', '✅ Login Successful!', 
                        `Authentication working correctly.<br><pre>${JSON.stringify({
                            success: data.success,
                            user: data.data?.user,
                            tokenLength: data.data?.token?.length
                        }, null, 2)}</pre>`);
                } else {
                    addResult('error', '❌ Login Failed', 
                        `HTTP ${response.status}: ${JSON.stringify(data, null, 2)}`);
                }
            } catch (error) {
                addResult('error', '❌ Login Error', 
                    `Failed to authenticate: ${error.message}`);
            }
        }

        // Auto-test connection on page load
        window.addEventListener('load', () => {
            setTimeout(testConnection, 500);
        });
    </script>
</body>
</html>
