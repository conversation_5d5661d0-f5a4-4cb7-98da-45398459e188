:root {
    --primary: #2D3436;
    --accent: #6C5CE7;
    --accent-secondary: #00B894;
    --accent-gradient: linear-gradient(135deg, #6C5CE7 0%, #00B894 100%);
    --text: #FFFFFF;
    --secondary: #A3A3A3;
    --card-bg: #696a6e;
    /*--card-bg: #363940;*/
    --font-primary: 'Plus Jakarta Sans', sans-serif;
    --font-secondary: 'Outfit', sans-serif;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    background-color: #F5F5F5;
    color: var(--text);
    font-family: var(--font-primary);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.nav {
    position: fixed;
    top: 0;
    width: 100%;
    padding: 1.5rem;
    background: #F5F5F5;
    backdrop-filter: blur(10px);
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: var(--primary);
    font-family: var(--font-secondary);
    font-weight: 600;
    text-decoration: none;
    position: relative;
    padding: 0.5rem;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-gradient);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.logo {
    font-size: clamp(2rem, 5vw, 4rem);
    font-weight: 800;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    font-family: var(--font-secondary);
    letter-spacing: -0.02em;
}

.hero {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #F5F5F5;
    padding: 6rem 2rem 2rem;
    text-align: center;
    color: var(--primary);
}

.hero-title {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.hero-section {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-text {
    font-size: clamp(1.5rem, 3vw, 2rem);
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 1rem 0;
}

.cta-group {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 20px;
}

.cta-button {
    background: var(--accent-gradient);
    color: var(--text);
    padding: 1rem 2rem;
    border-radius: 30px;
    border: none;
    font-size: 1.1rem;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
}


/* About Section */

.mission-vision {
    padding-top: 1rem;
    padding-bottom: 4rem;
    padding-left: 2rem;
    padding-right: 2rem;
}

.mission-vision .container {
    border-width: 5px;
    border-color: #00B894;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.mission, .vision {
    color: var(--accent-secondary);
    text-align: center;
    padding: 3rem 2rem;
}

.mission h2 p, .vision h2 p{
    color: var(--accent-secondary);
    font-style: var(--accent-secondary);
}

.mission i, .vision i {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.card {
    color: var(--primary);           
    background: #FFFFFF;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: 0.5s;
}

.card:hover::before {
    left: 100%;
}

.card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 12px 40px rgba(108, 92, 231, 0.2);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding-top: 2rem;
    padding-bottom: 4rem;
    max-width: 1200px;
    margin: 0 auto;
}

.value-card {
    text-align: center;
    padding: 2rem;
}

.value-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section-title {
    text-align: center;
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    margin-bottom: 0;
    color: var(--primary);
    -webkit-background-clip: text;
    background-clip: text;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.team-card {
    text-align: center;
    padding: 2rem;
}

.member-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 1.5rem;
    background: var(--accent-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.member-icon i {
    font-size: 2.5rem;
    color: var(--text);
}

.position {
    color: var(--accent);
    margin: 0.5rem 0;
}

.bio {
    color: var(--secondary);
    font-size: 0.9rem;
}

/* Home Section */
.testimonials {
    padding: 4rem 2rem;
    background: #f5f5f5;
}

.testimonials-grid {
    padding-top: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    background: var(--text);
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    transition: transform 0.3s ease;
}

.quote {
    font-size: 1.1rem;
    font-style: italic;
}

.client-info {
    margin-top: auto;
}

.client-name {
    font-weight: 600;
    color: var(--accent);
    margin-bottom: 0.5rem;
}

.client-position {
    font-size: 0.9rem;
    color: var(--secondary);
}

.client-quote {
    font-size: 1.1rem;
    color: var(--text);
    margin-bottom: 1.5rem;
}

/* Services Section */
.services-container {
    display: grid;
    grid-template-columns: repeat(2, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.services-container2 {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: 2rem;
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 2rem;
    padding-bottom: 4rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem;
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.service-features {
    list-style: none;
    margin: 1.5rem 0;
    text-align: left;
}

.service-features li {
    margin: 0.8rem 0;
}

.service-features i {
    color: var(--accent);
    margin-right: 0.5rem;
}

.specialized-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding-top: 2rem;
    padding-bottom: 4rem;
    max-width: 1200px;
    margin: 0 auto;
}

.specialized-card {
    text-align: center;
    padding: 2rem;
}

.specialized-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.process-timeline {
    color: var(--primary);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    padding: 4rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.process-step {
    text-align: center;
    position: relative;
}

.step-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: var(--accent-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step-icon i {
    font-size: 2rem;
    color: var(--text);
}

/* Portfolio Section */
.cta {
    display: inline-block;
    padding: 12px 24px;
    background: white;
    color: #3a86ff;
    text-decoration: none;
    font-weight: bold;
    border-radius: 8px;
    transition: background 0.3s;
}
  
.cta:hover {
    background: #edf2ff;
}
  
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    padding: 60px 20px;
    max-width: 1200px;
    margin: auto;
}
  
.project-card {
    color: var(--primary);
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}
  
.project-card:hover {
    transform: translateY(-5px);
}
  
.project-card img {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 15px;
}
  
.project-card h3 {
    margin: 10px 0 5px;
}
  
.project-card a {
    color: #3a86ff;
    text-decoration: none;
    font-weight: bold;
}
  
.cta-bottom {
    text-align: center;
    padding: 60px 20px;
    color: var(--primary);
}
  
.cta-bottom h2 {
    font-size: 2rem;
    margin-bottom: 20px;
}

/* Contact Section */
.contact-form {
    padding: 4rem 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.contact-grid {
    display: grid;
    gap: 1.5rem;
}

.contact-grid input,
.contact-grid textarea {
    background: var(--card-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    color: var(--text);
    font-family: var(--font-primary);
}

.submit-btn {
    background: var(--accent-gradient);
    color: var(--text);
    padding: 1rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.info-card {
    text-align: center;
    padding: 2rem;
}

.info-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.form-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
}

.form-container h2 {
    text-align: center;
}

.contact-form {
    display: grid;
    gap: 1.5rem;
}

.form-group {
    display: grid;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
}

.form-group input,
.form-group textarea {
    padding: 0.8rem;
    border: 1px solid var(--secondary);
    border-radius: 8px;
    background: #f5f5f5;
    color: black;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent);
}

.faq-section {
    margin: 60px 0;
}

.faq-section h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #007bff;
}

.faq-item {
    margin-bottom: 20px;
    border: 1px solid var(--accent);
    border-radius: 5px;
    overflow: hidden;
}

.faq-question {
    color: var(--primary);
    padding: 15px;
    background: #f5f5f5;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-question i {
    color: var(--primary);
}

.faq-answer {
    padding: 0 15px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 15px;
    max-height: 500px;
}

.faq-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Contact Form Messages */
.contact-message {
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
}

.contact-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.contact-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* FAQ Interactive Styles */
.faq-question {
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background-color: rgba(108, 92, 231, 0.1);
}

.faq-question i {
    transition: transform 0.3s ease;
}

.faq-answer {
    display: none;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Quotation Page */
/* Pricing Section */
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding-left: 2rem;
    padding-right: 2rem;
    padding-top: 2rem;
    padding-bottom: 4rem;
}

.pricing-card {
    color: var(--primary);
    background: #F5F5F5;
    border: 2px solid;
    border-radius: 16px;
    padding: 2rem;
    position: relative;
    transition: transform 0.3s ease;
}

.pricing-card.featured {
    border: 2px solid;
    background: #F5F5F5;
    transform: scale(1.05);
}

.featured-tag {
    position: absolute;
    top: -12px;
    right: 20px;
    background: var(--accent-gradient);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.btn-primary {
    background: var(--accent-gradient);
    color: var(--text);
    padding: 1rem 2rem;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    transition: opacity 0.3s ease;
}

.btn-primary:hover {
    opacity: 0.9;
}

/* Custom Quote Form */
.quote-form {
    background: #F5F5F5;
    border-radius: 16px;
    border: 2px solid var(--secondary);
    padding: 2rem;
    margin: 0 2rem;
    margin-bottom: 4rem;
}

.form-section {
    margin-bottom: 2rem;
}

.form-section h3 {
    text-align: center;
    font: 1.5em sans-serif;
    font-weight: 600;
    color: var(--accent);
    margin-bottom: 1rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--primary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid var(--secondary);
    background: #F5F5F5;
    color: black;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 4rem;
}

.feature-card {
    background: var(--secondary);
    color: #000;
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature-card:hover {
    transform: translateY(-3px);
}

.form-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--secondary);
}

.estimated-price {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--accent);
}

/* Footer */
footer {
    background: #333;
    color: #fff;
    padding: 60px 0 20px;
    padding-left: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 20px;
}

.footer-section h3 {
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #ddd;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #007bff;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    color: #fff;
    font-size: 1.5rem;
}

a {
    text-decoration: none;
    color: #333;
}

ul {
    list-style: none;
}

.copyright {
    background: #333;
    text-align: center;
    padding: 20px 0;
    border-top: 1px solid #444;
}

/* API Integration Styles */
.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-size: 1.1rem;
}

.loading i {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    text-align: center;
    padding: 2rem;
    color: #dc2626;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    margin: 1rem 0;
}

.error i {
    margin-right: 0.5rem;
}

.no-data {
    text-align: center;
    padding: 3rem;
    color: #666;
    font-style: italic;
}

/* Enhanced Service Cards */
.service-card {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.service-meta {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    font-size: 0.85rem;
    color: #666;
}

/* Enhanced Project Cards */
.project-card {
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
}

.project-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

.project-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-content {
    padding: 1.5rem;
}

.tech-stack {
    margin: 1rem 0;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
}

.project-links {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 1rem;
    background: #4f46e5;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 0.85rem;
    transition: background 0.3s ease;
}

.project-link:hover {
    background: #3730a3;
    color: white;
}

/* Enhanced Testimonial Cards */
.testimonial-card {
    position: relative;
    padding: 2rem;
}

.quote-icon {
    position: absolute;
    top: 1rem;
    left: 1.5rem;
    font-size: 2rem;
    color: #4f46e5;
    opacity: 0.3;
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-content blockquote {
    font-style: italic;
    margin: 1rem 0;
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

.rating {
    color: #fbbf24;
    font-size: 1.2rem;
    margin-top: 1rem;
}

.testimonial-author {
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.author-info h4 {
    margin: 0 0 0.25rem 0;
    color: #333;
}

.author-info p {
    margin: 0.25rem 0;
    color: #666;
    font-size: 0.9rem;
}

.company {
    font-weight: 600;
    color: #4f46e5;
}

/* Responsive Grid Improvements */
.services-container,
.services-container2 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.portfolio-grid,
.projects-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.testimonials-container,
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .services-container,
    .services-container2,
    .portfolio-grid,
    .projects-container,
    .testimonials-container,
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .project-links {
        flex-direction: column;
    }

    .project-link {
        text-align: center;
    }

    .nav-links {
        display: none;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .cta-group {
        flex-direction: column;
        align-items: center;
    }
}
