// Eclipse Softworks Frontend API Integration
// Backend API Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// API Helper Functions
class EclipseAPI {
  static async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  // Services API
  static async getServices() {
    return this.request('/services');
  }

  static async getService(id) {
    return this.request(`/services/${id}`);
  }

  // Portfolio API
  static async getPortfolio() {
    return this.request('/portfolio');
  }

  static async getProject(id) {
    return this.request(`/portfolio/${id}`);
  }

  // Testimonials API
  static async getTestimonials() {
    return this.request('/testimonials');
  }

  static async getTestimonial(id) {
    return this.request(`/testimonials/${id}`);
  }

  // Contact API
  static async submitContactMessage(contactData) {
    return this.request('/contact', {
      method: 'POST',
      body: JSON.stringify(contactData)
    });
  }

  // Authentication API
  static async login(credentials) {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    });
  }

  // Admin API (requires authentication)
  static async createService(serviceData, token) {
    return this.request('/services', {
      method: 'POST',
      headers: { Authorization: `Bearer ${token}` },
      body: JSON.stringify(serviceData)
    });
  }

  static async updateService(id, serviceData, token) {
    return this.request(`/services/${id}`, {
      method: 'PUT',
      headers: { Authorization: `Bearer ${token}` },
      body: JSON.stringify(serviceData)
    });
  }

  static async deleteService(id, token) {
    return this.request(`/services/${id}`, {
      method: 'DELETE',
      headers: { Authorization: `Bearer ${token}` }
    });
  }

  static async changePassword(currentPassword, newPassword, token) {
    return this.request('/auth/change-password', {
      method: 'POST',
      headers: { Authorization: `Bearer ${token}` },
      body: JSON.stringify({ currentPassword, newPassword })
    });
  }
}

// UI Helper Functions
class UIHelpers {
  static showLoading(element) {
    element.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
  }

  static showError(element, message) {
    element.innerHTML = `<div class="error"><i class="fas fa-exclamation-triangle"></i> ${message}</div>`;
  }

  static formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  static createServiceCard(service) {
    return `
      <div class="service-card card" data-service-id="${service.id}">
        <div class="service-icon"><i class="${service.icon}"></i></div>
        <h3>${service.title}</h3>
        <p>${service.description}</p>
        <div class="service-meta">
          <small>Updated: ${this.formatDate(service.updated_at)}</small>
        </div>
      </div>
    `;
  }

  static createProjectCard(project) {
    return `
      <div class="project-card card" data-project-id="${project.id}">
        <div class="project-image">
          <img src="${project.image_url}" alt="${project.title}" loading="lazy">
        </div>
        <div class="project-content">
          <h3>${project.title}</h3>
          <p>${project.description}</p>
          <div class="tech-stack">
            <strong>Tech Stack:</strong> ${project.tech_stack}
          </div>
          <div class="project-links">
            ${project.project_url ? `<a href="${project.project_url}" target="_blank" class="project-link"><i class="fas fa-external-link-alt"></i> Live Demo</a>` : ''}
            ${project.github_url ? `<a href="${project.github_url}" target="_blank" class="project-link"><i class="fab fa-github"></i> GitHub</a>` : ''}
          </div>
        </div>
      </div>
    `;
  }

  static createTestimonialCard(testimonial) {
    const stars = '★'.repeat(testimonial.rating) + '☆'.repeat(5 - testimonial.rating);
    return `
      <div class="testimonial-card card" data-testimonial-id="${testimonial.id}">
        <div class="testimonial-content">
          <div class="quote-icon"><i class="fas fa-quote-left"></i></div>
          <blockquote>"${testimonial.quote}"</blockquote>
          <div class="rating">${stars}</div>
        </div>
        <div class="testimonial-author">
          <div class="author-info">
            <h4>${testimonial.client_name}</h4>
            <p>${testimonial.client_position}</p>
            ${testimonial.company ? `<p class="company">${testimonial.company}</p>` : ''}
          </div>
        </div>
      </div>
    `;
  }
}

// Page-specific functionality
class PageManager {
  static async loadServices() {
    const servicesContainer = document.querySelector('.services-container, .services-container2');
    if (!servicesContainer) return;

    UIHelpers.showLoading(servicesContainer);

    try {
      const response = await EclipseAPI.getServices();
      const services = response.data;

      if (services.length === 0) {
        servicesContainer.innerHTML = '<p class="no-data">No services available at the moment.</p>';
        return;
      }

      servicesContainer.innerHTML = services.map(service =>
        UIHelpers.createServiceCard(service)
      ).join('');

      // Add click handlers for service cards
      servicesContainer.querySelectorAll('.service-card').forEach(card => {
        card.addEventListener('click', () => {
          const serviceId = card.dataset.serviceId;
          // You can add modal or navigation logic here
          console.log('Service clicked:', serviceId);
        });
      });

    } catch (error) {
      UIHelpers.showError(servicesContainer, 'Failed to load services. Please try again later.');
    }
  }

  static async loadPortfolio() {
    const portfolioContainer = document.querySelector('.portfolio-grid, .projects-container');
    if (!portfolioContainer) return;

    UIHelpers.showLoading(portfolioContainer);

    try {
      const response = await EclipseAPI.getPortfolio();
      const projects = response.data;

      if (projects.length === 0) {
        portfolioContainer.innerHTML = '<p class="no-data">No projects available at the moment.</p>';
        return;
      }

      portfolioContainer.innerHTML = projects.map(project =>
        UIHelpers.createProjectCard(project)
      ).join('');

      // Add click handlers for project cards
      portfolioContainer.querySelectorAll('.project-card').forEach(card => {
        card.addEventListener('click', () => {
          const projectId = card.dataset.projectId;
          console.log('Project clicked:', projectId);
        });
      });

    } catch (error) {
      UIHelpers.showError(portfolioContainer, 'Failed to load portfolio. Please try again later.');
    }
  }

  static async loadTestimonials() {
    const testimonialsContainer = document.querySelector('.testimonials-container, .testimonials-grid');
    if (!testimonialsContainer) return;

    UIHelpers.showLoading(testimonialsContainer);

    try {
      const response = await EclipseAPI.getTestimonials();
      const testimonials = response.data;

      if (testimonials.length === 0) {
        testimonialsContainer.innerHTML = '<p class="no-data">No testimonials available at the moment.</p>';
        return;
      }

      testimonialsContainer.innerHTML = testimonials.map(testimonial =>
        UIHelpers.createTestimonialCard(testimonial)
      ).join('');

    } catch (error) {
      UIHelpers.showError(testimonialsContainer, 'Failed to load testimonials. Please try again later.');
    }
  }

  // Load homepage services (limited to 3-4 items)
  static async loadHomepageServices() {
    const servicesContainer = document.querySelector('.services-container');
    if (!servicesContainer) return;

    try {
      const response = await EclipseAPI.getServices();
      const services = response.data.slice(0, 4); // Limit to 4 services for homepage

      // Update existing service cards with real data
      const serviceCards = servicesContainer.querySelectorAll('.service-card');

      services.forEach((service, index) => {
        if (serviceCards[index]) {
          const card = serviceCards[index];
          const icon = card.querySelector('.service-icon i');
          const title = card.querySelector('h3');
          const description = card.querySelector('p');

          if (icon) icon.className = service.icon;
          if (title) title.textContent = service.title;
          if (description) description.textContent = service.description;

          card.dataset.serviceId = service.id;
        }
      });

    } catch (error) {
      console.error('Failed to load homepage services:', error);
      // Keep existing static content if API fails
    }
  }
}

// Authentication Manager
class AuthManager {
  static getToken() {
    return localStorage.getItem('eclipse_auth_token');
  }

  static setToken(token) {
    localStorage.setItem('eclipse_auth_token', token);
  }

  static removeToken() {
    localStorage.removeItem('eclipse_auth_token');
  }

  static isAuthenticated() {
    const token = this.getToken();
    if (!token) return false;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp > Date.now() / 1000;
    } catch {
      return false;
    }
  }

  static async login(username, password) {
    try {
      const response = await EclipseAPI.login({ username, password });
      this.setToken(response.data.token);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  static logout() {
    this.removeToken();
    window.location.href = 'index.html';
  }
}

// Initialize page content when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  const currentPage = window.location.pathname.split('/').pop() || 'index.html';

  // Load content based on current page
  switch(currentPage) {
    case 'index.html':
    case '':
      PageManager.loadHomepageServices();
      break;

    case 'services.html':
      PageManager.loadServices();
      break;

    case 'portfolio.html':
      PageManager.loadPortfolio();
      break;

    case 'testimonials.html':
      PageManager.loadTestimonials();
      break;

    case 'contact.html':
      ContactFormHandler.init();
      FAQHandler.init();
      break;
  }

  // Initialize contact form and FAQ on all pages (in case they exist)
  ContactFormHandler.init();
  FAQHandler.init();

  // Add admin panel toggle (for development/testing)
  if (window.location.search.includes('admin=true')) {
    createAdminPanel();
  }
});

// Admin Panel for Testing (Development Only)
function createAdminPanel() {
  const adminPanel = document.createElement('div');
  adminPanel.id = 'admin-panel';
  adminPanel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    background: #fff;
    border: 2px solid #333;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
    max-width: 300px;
  `;

  adminPanel.innerHTML = `
    <h4>Admin Panel</h4>
    <div id="auth-status">Not logged in</div>
    <div id="login-form" style="margin-top: 10px;">
      <input type="text" id="admin-username" placeholder="Username" style="width: 100%; margin-bottom: 5px; padding: 5px;">
      <input type="password" id="admin-password" placeholder="Password" style="width: 100%; margin-bottom: 5px; padding: 5px;">
      <button id="login-btn" style="width: 100%; padding: 5px;">Login</button>
    </div>
    <div id="admin-actions" style="display: none; margin-top: 10px;">
      <button id="refresh-data" style="width: 100%; margin-bottom: 5px; padding: 5px;">Refresh Data</button>
      <button id="logout-btn" style="width: 100%; padding: 5px;">Logout</button>
    </div>
  `;

  document.body.appendChild(adminPanel);

  // Admin panel event handlers
  document.getElementById('login-btn').addEventListener('click', async () => {
    const username = document.getElementById('admin-username').value;
    const password = document.getElementById('admin-password').value;

    try {
      await AuthManager.login(username, password);
      updateAuthStatus();
    } catch (error) {
      alert('Login failed: ' + error.message);
    }
  });

  document.getElementById('logout-btn').addEventListener('click', () => {
    AuthManager.logout();
  });

  document.getElementById('refresh-data').addEventListener('click', () => {
    location.reload();
  });

  function updateAuthStatus() {
    const authStatus = document.getElementById('auth-status');
    const loginForm = document.getElementById('login-form');
    const adminActions = document.getElementById('admin-actions');

    if (AuthManager.isAuthenticated()) {
      authStatus.textContent = 'Logged in as admin';
      loginForm.style.display = 'none';
      adminActions.style.display = 'block';
    } else {
      authStatus.textContent = 'Not logged in';
      loginForm.style.display = 'block';
      adminActions.style.display = 'none';
    }
  }

  updateAuthStatus();
}

// Contact Form Handler
class ContactFormHandler {
  static init() {
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
      contactForm.addEventListener('submit', this.handleSubmit.bind(this));
    }
  }

  static async handleSubmit(event) {
    event.preventDefault();

    const form = event.target;
    const submitBtn = form.querySelector('.submit-btn');
    const originalText = submitBtn.textContent;

    // Get form data
    const formData = new FormData(form);
    const contactData = {
      name: formData.get('name').trim(),
      email: formData.get('email').trim(),
      phone: formData.get('phone').trim(),
      message: formData.get('message').trim()
    };

    // Basic validation
    if (!contactData.name || !contactData.email || !contactData.message) {
      this.showMessage('Please fill in all required fields.', 'error');
      return;
    }

    if (contactData.message.length < 10) {
      this.showMessage('Message must be at least 10 characters long.', 'error');
      return;
    }

    // Show loading state
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;

    try {
      const response = await EclipseAPI.submitContactMessage(contactData);

      if (response.success) {
        this.showMessage(response.message || 'Thank you for your message! We will get back to you soon.', 'success');
        form.reset();
      } else {
        throw new Error(response.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Contact form error:', error);
      this.showMessage('Sorry, there was an error sending your message. Please try again later.', 'error');
    } finally {
      // Restore button state
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  }

  static showMessage(message, type) {
    // Remove any existing messages
    const existingMessage = document.querySelector('.contact-message');
    if (existingMessage) {
      existingMessage.remove();
    }

    // Create new message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `contact-message ${type}`;
    messageDiv.textContent = message;

    // Insert message before the form
    const form = document.getElementById('contactForm');
    if (form) {
      form.parentNode.insertBefore(messageDiv, form);

      // Auto-remove success messages after 5 seconds
      if (type === 'success') {
        setTimeout(() => {
          if (messageDiv.parentNode) {
            messageDiv.remove();
          }
        }, 5000);
      }
    }
  }
}

// FAQ Toggle Handler
class FAQHandler {
  static init() {
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      if (question) {
        question.addEventListener('click', () => this.toggleFAQ(item));
      }
    });
  }

  static toggleFAQ(item) {
    const answer = item.querySelector('.faq-answer');
    const icon = item.querySelector('.faq-question i');

    if (answer && icon) {
      const isOpen = answer.style.display === 'block';

      // Close all other FAQs
      document.querySelectorAll('.faq-item .faq-answer').forEach(ans => {
        ans.style.display = 'none';
      });
      document.querySelectorAll('.faq-item .faq-question i').forEach(ic => {
        ic.style.transform = 'rotate(0deg)';
      });

      // Toggle current FAQ
      if (!isOpen) {
        answer.style.display = 'block';
        icon.style.transform = 'rotate(180deg)';
      }
    }
  }
}

// Global error handler
window.addEventListener('unhandledrejection', function(event) {
  console.error('Unhandled promise rejection:', event.reason);
});

// Export for global access (if needed)
window.EclipseAPI = EclipseAPI;
window.AuthManager = AuthManager;
window.PageManager = PageManager;
window.ContactFormHandler = ContactFormHandler;
window.FAQHandler = FAQHandler;