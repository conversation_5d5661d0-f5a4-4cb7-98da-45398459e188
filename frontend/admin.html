<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Admin Panel - Eclipse Softworks</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image.jpg" href="assets/images/Favicon.JPG" class="favicon">
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;600;800&family=Plus+Jakarta+Sans:wght@400;600;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .admin-header {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary);
        }
        
        .login-section, .admin-section {
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--primary);
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        .btn {
            background: var(--accent-gradient);
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .status-message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .hidden {
            display: none;
        }
        
        .admin-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--accent);
        }
        
        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <nav class="nav">
        <div class="logo" style="font-size: 1.5rem;"><i class="fas fa-eclipse"></i> Eclipse Softworks</div>
        <div class="nav-links">
            <a href="index.html" class="nav-link"><i class="fas fa-home"></i> Home</a>
            <a href="about.html" class="nav-link"><i class="fas fa-users"></i> About Us</a>
            <a href="services.html" class="nav-link"><i class="fas fa-cogs"></i> Services</a>
            <a href="portfolio.html" class="nav-link"><i class="fas fa-briefcase"></i> Portfolio</a>
            <a href="contact.html" class="nav-link"><i class="fas fa-envelope"></i> Contact Us</a>
        </div>
    </nav>

    <div class="admin-container" style="margin-top: 6rem;">
        <div class="admin-header">
            <h1><i class="fas fa-shield-alt"></i> Admin Panel</h1>
            <p>Manage your Eclipse Softworks content</p>
        </div>

        <div id="status-message" class="status-message hidden"></div>

        <!-- Login Section -->
        <div id="login-section" class="login-section">
            <h2>Admin Login</h2>
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="admin" placeholder="Enter username">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter password">
            </div>
            <button class="btn" onclick="login()">
                <i class="fas fa-sign-in-alt"></i> Login
            </button>
        </div>

        <!-- Admin Section (hidden by default) -->
        <div id="admin-section" class="admin-section hidden">
            <div class="admin-stats">
                <div class="stat-card">
                    <div class="stat-number" id="services-count">-</div>
                    <div class="stat-label">Services</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="projects-count">-</div>
                    <div class="stat-label">Projects</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="testimonials-count">-</div>
                    <div class="stat-label">Testimonials</div>
                </div>
            </div>

            <h2>Change Password</h2>
            <div class="form-group">
                <label for="current-password">Current Password:</label>
                <input type="password" id="current-password" placeholder="Enter current password">
            </div>
            <div class="form-group">
                <label for="new-password">New Password:</label>
                <input type="password" id="new-password" placeholder="Enter new password">
            </div>
            <div class="form-group">
                <label for="confirm-password">Confirm New Password:</label>
                <input type="password" id="confirm-password" placeholder="Confirm new password">
            </div>
            <button class="btn" onclick="changePassword()">
                <i class="fas fa-key"></i> Change Password
            </button>

            <h2>Quick Actions</h2>
            <button class="btn" onclick="refreshData()">
                <i class="fas fa-sync"></i> Refresh Data
            </button>
            <button class="btn btn-secondary" onclick="viewLogs()">
                <i class="fas fa-list"></i> View Logs
            </button>
            <button class="btn btn-danger" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
    </div>

    <script src="assets/script.js"></script>
    <script>
        let authToken = null;

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (!username || !password) {
                showMessage('Please enter both username and password', 'error');
                return;
            }

            try {
                const response = await EclipseAPI.login({ username, password });
                authToken = response.data.token;
                AuthManager.setToken(authToken);
                
                showMessage('Login successful!', 'success');
                document.getElementById('login-section').classList.add('hidden');
                document.getElementById('admin-section').classList.remove('hidden');
                
                await loadStats();
            } catch (error) {
                showMessage('Login failed: ' + error.message, 'error');
            }
        }

        async function changePassword() {
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            if (!currentPassword || !newPassword || !confirmPassword) {
                showMessage('Please fill in all password fields', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                showMessage('New passwords do not match', 'error');
                return;
            }

            if (newPassword.length < 6) {
                showMessage('New password must be at least 6 characters long', 'error');
                return;
            }

            try {
                await EclipseAPI.changePassword(currentPassword, newPassword, authToken);

                showMessage('Password changed successfully!', 'success');

                // Clear password fields
                document.getElementById('current-password').value = '';
                document.getElementById('new-password').value = '';
                document.getElementById('confirm-password').value = '';

            } catch (error) {
                showMessage('Failed to change password: ' + error.message, 'error');
            }
        }

        async function loadStats() {
            try {
                const [services, projects, testimonials] = await Promise.all([
                    EclipseAPI.getServices(),
                    EclipseAPI.getPortfolio(),
                    EclipseAPI.getTestimonials()
                ]);

                document.getElementById('services-count').textContent = services.count;
                document.getElementById('projects-count').textContent = projects.count;
                document.getElementById('testimonials-count').textContent = testimonials.count;
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        function refreshData() {
            location.reload();
        }

        function viewLogs() {
            showMessage('Log viewing functionality would be implemented here', 'success');
        }

        function logout() {
            AuthManager.logout();
        }

        function showMessage(message, type) {
            const messageEl = document.getElementById('status-message');
            messageEl.textContent = message;
            messageEl.className = `status-message ${type}`;
            messageEl.classList.remove('hidden');
            
            setTimeout(() => {
                messageEl.classList.add('hidden');
            }, 5000);
        }

        // Check if already logged in
        if (AuthManager.isAuthenticated()) {
            authToken = AuthManager.getToken();
            document.getElementById('login-section').classList.add('hidden');
            document.getElementById('admin-section').classList.remove('hidden');
            loadStats();
        }
    </script>
</body>
</html>
